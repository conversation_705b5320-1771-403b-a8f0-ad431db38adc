# Match Rules CRUD Operations Summary

## Overview
This document outlines the complete CRUD (Create, Read, Update, Delete) operations for matching rules in the thesis builder.

## 🔗 Complete Flow

### **CREATE Operation**
1. **UI**: User clicks "Add Matching Rule" button
2. **Local State**: `handleAddLocalMatchRule()` adds rule to `localMatchRules` array
3. **UI**: User fills in rule details (name, description, conditions)
4. **Save**: User clicks "Save" button on the rule card
5. **API**: `handleSaveLocalMatchRule()` → `createMatchRule()` → `ThesisAPI.createMatchRule()`
6. **Backend**: `POST /thesis/{thesisId}/match-rules`
7. **Response**: Returns full `ThesisWithRules` object
8. **State Update**: Hook updates global state via `setThesisState()`
9. **Local Sync**: `useEffect` syncs local state with global state

### **READ Operation**
1. **Initial Load**: `useThesisManager` loads thesis with rules
2. **API**: `ThesisAPI.getThesis(thesisId)`
3. **Backend**: `GET /thesis/{thesisId}`
4. **Response**: Returns `ThesisWithRules` with `match_rules` array
5. **State**: Rules stored in `thesisState.matchRules`
6. **Local Sync**: Rules copied to `localMatchRules` for editing

### **UPDATE Operation**
1. **UI**: User modifies rule details in expanded card
2. **Local State**: `handleUpdateLocalMatchRule()` updates `localMatchRules[index]`
3. **Save**: User clicks "Save" button
4. **API**: `handleSaveLocalMatchRule()` → `updateMatchRule()` → `ThesisAPI.updateMatchRule()`
5. **Backend**: `PUT /thesis/match-rules/{ruleId}`
6. **Response**: Returns full `ThesisWithRules` object
7. **State Update**: Hook updates global state
8. **Local Sync**: `useEffect` syncs local state

### **DELETE Operation**
1. **UI**: User clicks delete button (trash icon) on saved rule
2. **API**: `onDeleteMatchRule()` → `deleteMatchRule()` → `ThesisAPI.deleteMatchRule()`
3. **Backend**: `DELETE /thesis/match-rules/{ruleId}`
4. **Response**: Returns success message
5. **State Update**: Hook refreshes full thesis state via `ThesisAPI.getThesis()`
6. **Local Update**: `setLocalMatchRules()` filters out deleted rule immediately
7. **UI**: Rule disappears from list

## 🎯 API Endpoints

| Operation | Method | Endpoint | Request Body | Response |
|-----------|--------|----------|--------------|----------|
| Create | POST | `/thesis/{thesisId}/match-rules` | `MatchRuleCreateRequest` | `ThesisWithRules` |
| Read | GET | `/thesis/{thesisId}` | - | `ThesisWithRules` |
| Update | PUT | `/thesis/match-rules/{ruleId}` | `Partial<MatchRuleCreateRequest>` | `ThesisWithRules` |
| Delete | DELETE | `/thesis/match-rules/{ruleId}` | - | `{message: string}` |

## 🔄 State Management

### Global State (useThesisManager)
- `thesisState.matchRules`: Authoritative source from backend
- Updated via API responses
- Normalized with `_id` field

### Local State (thesis-builder)
- `localMatchRules`: Working copy for immediate UI updates
- Synced with global state via `useEffect`
- Allows smooth editing experience

## 🎨 UI Components

### MatchingConfigBuilder
- Container component
- Manages list of rules
- Handles bulk operations ("Save All")

### MatchRuleCard
- Individual rule editor
- Shows save/delete buttons based on rule state
- Handles validation and loading states

## 🚨 Error Handling

### Validation
- Rule name required
- At least one condition required
- All conditions must have valid question and values

### API Errors
- Network errors caught and displayed via toast
- Loading states shown during operations
- Optimistic updates rolled back on failure

## 🔍 Debug Information

### Console Logs
- All API calls logged with operation names
- State updates logged with rule details
- Errors logged with full context

### State Indicators
- Unsaved rules marked with amber border
- Save buttons only shown for dirty rules
- Loading spinners during API operations

## ✅ Features Working

✅ **Create**: New rules added locally, saved via API
✅ **Read**: Rules loaded from backend on page load
✅ **Update**: Rule changes saved via API with proper state sync
✅ **Delete**: Rules deleted via API with immediate UI update
✅ **Validation**: Proper validation before save operations
✅ **Error Handling**: Toast notifications for all operations
✅ **Loading States**: Spinners and disabled states during operations
✅ **Local State Sync**: Smooth editing with proper state management

## 🐛 Known Issues

None currently identified. All CRUD operations functioning correctly.

## 🧪 Testing

To test CRUD operations:
1. Navigate to thesis builder
2. Add a new match rule
3. Fill in details and save
4. Edit the saved rule
5. Delete the rule
6. Verify all operations work and UI updates correctly 