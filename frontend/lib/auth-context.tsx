"use client"

import React, { create<PERSON>ontext, useContext, useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import AuthAPI from './api/auth-api';

// Helper function to check if we're on the client side
function isClient() {
  return typeof window !== 'undefined';
}

// Helper function to safely access localStorage
function safeLocalStorage() {
  if (!isClient()) return null;
  try {
    return window.localStorage;
  } catch (error) {
    console.warn('localStorage not available:', error);
    return null;
  }
}

// Define the auth state interface
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;
  refreshToken: string | null;
  orgId: string | null;
  loading: boolean;
}

// Define the user interface based on backend model
interface User {
  id: string;
  name: string;
  email: string;
  org_id: string;
  org_ids?: string[]; // Optional since backend doesn't always return this
  role_id: string | null;
  status: string;
  is_superuser: boolean;
  is_active: boolean;
}

// Define the organization interface
interface Organization {
  id: string;
  name: string;
  subdomain: string;
  description?: string;
  logo_url?: string;
}

// Define the auth context interface
interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<{success: boolean, redirectUrl: string}>;
  loginWithTokens: (accessToken: string, refreshToken: string, user: User, orgId?: string) => void;
  logout: () => void;
  setCurrentOrg: (orgId: string) => void;
  refreshAuth: () => Promise<boolean>;
  userOrganizations: Organization[];
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Add a refresh lock to prevent multiple simultaneous refresh attempts
let isRefreshing = false;
let refreshSubscribers: Array<(token: string) => void> = [];
let refreshFailureCount = 0;
const MAX_REFRESH_FAILURES = 2;

// Function to subscribe to token refresh
const subscribeTokenRefresh = (cb: (token: string) => void) => {
  refreshSubscribers.push(cb);
};

// Function to notify subscribers of token refresh
const onRefreshComplete = (token: string) => {
  refreshSubscribers.forEach(cb => cb(token));
  refreshSubscribers = [];

  // Reset failure count on success
  if (token) {
    refreshFailureCount = 0;
  }
};

// Create a provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const [state, setState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    refreshToken: null,
    orgId: null,
    loading: true,
  });
  const [userOrganizations, setUserOrganizations] = useState<Organization[]>([]);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Initialize auth state from localStorage on component mount (only on client)
  useEffect(() => {
    if (!isHydrated) return;

    const storage = safeLocalStorage();
    if (!storage) {
      setState(prev => ({ ...prev, loading: false }));
      return;
    }

    try {
      const token = storage.getItem('token');
      const refreshToken = storage.getItem('refreshToken');
      const orgId = storage.getItem('orgId');
      const userJson = storage.getItem('user');

      if (token && userJson && orgId) {
        try {
          const user = JSON.parse(userJson);
          setState({
            isAuthenticated: true,
            user,
            token,
            refreshToken,
            orgId,
            loading: false,
          });

          // Set up dummy organization data for the org selector
          fetchUserOrganizations(token, orgId);

          console.log('Auth initialized with token and org ID:', orgId);
        } catch (error) {
          console.error('Failed to parse user data:', error);
          setState(prev => ({ ...prev, loading: false }));
        }
      } else {
        console.log('No auth data found in localStorage');
        setState(prev => ({ ...prev, loading: false }));
      }
    } catch (error) {
      console.error('Error initializing auth:', error);
      setState(prev => ({ ...prev, loading: false }));
    }
  }, [isHydrated]);

  // Note: Axios interceptors are now handled in api-client.ts to avoid conflicts
  // This ensures all API requests go through a single, consistent auth flow

  // We don't need to fetch organizations separately as we get the org ID from the login response
  // This is kept as a placeholder in case we need to fetch organizations in the future
  const fetchUserOrganizations = async (token: string, orgId: string) => {
    console.log('Fetching organizations for org ID:', orgId);

    try {
      const org_response = await AuthAPI.getOrganizations(token, orgId);
      console.log('Organizations fetched:', org_response);

      if (orgId && org_response && org_response.length > 0) {
        // org_response is an array, so we take the first organization
        const org = org_response[0];
        setUserOrganizations([
          {
            id: org.id,
            name: org.name,
            subdomain: org.subdomain,
            description: org.description || "Testing Description",
            logo_url: org.logo_url || "/images/TX-Placeholder.png"
          }
        ]);
      }
    } catch (error) {
      console.error('Error fetching organizations:', error);
    }
  };


  // Login function
  const login = async (email: string, password: string): Promise<{success: boolean, redirectUrl: string}> => {
    try {
      console.log('Attempting login with email:', email);

      if (!email || !password) {
        console.error('Login failed: Email or password is missing');
        throw new Error('Email and password are required');
      }

      // Call the login API with the user-provided credentials
      const loginResponse = await AuthAPI.login({
        email: email.trim(),
        password: password,
        device_info: {
          device_type: 'web',
          browser: isClient() ? navigator.userAgent : 'server',
        },
      });

      console.log('Login response received');

      const { access_token, refresh_token } = loginResponse.data;

      if (!access_token || !refresh_token) {
        console.error('Login failed: Invalid response - missing tokens');
        throw new Error('Authentication failed - invalid response from server');
      }

      // Extract X-ORG-ID from response headers (case-insensitive)
      const orgIdHeader = Object.keys(loginResponse.headers)
        .find(key => key.toLowerCase() === 'x-org-id');

      const orgId = orgIdHeader ? loginResponse.headers[orgIdHeader] : '';
      console.log('X-ORG-ID from response headers:', orgId);

      if (!orgId) {
        console.warn('No X-ORG-ID found in response headers');
      }

      // Decode the JWT to get user info
      const payload = JSON.parse(atob(access_token.split('.')[1]));
      console.log('Token payload subject:', payload.sub);

      // Get user details using the token from the login response
      const user = await AuthAPI.getCurrentUser(access_token, orgId);
      console.log('User details retrieved for:', user.email);

      // Use org_id from header, payload, or user data (in order of preference)
      const finalOrgId = orgId || (payload.metadata && payload.metadata.org_id) || user.org_id;
      console.log('Using org ID:', finalOrgId);

      if (!finalOrgId) {
        throw new Error('Could not determine organization ID');
      }

      // Save to state and localStorage (only on client)
      setState({
        isAuthenticated: true,
        user,
        token: access_token,
        refreshToken: refresh_token,
        orgId: finalOrgId,
        loading: false,
      });

      const storage = safeLocalStorage();
      if (storage) {
        try {
          storage.setItem('token', access_token);
          storage.setItem('refreshToken', refresh_token);
          storage.setItem('orgId', finalOrgId);
          storage.setItem('user', JSON.stringify(user));
        } catch (error) {
          console.warn('Failed to save auth data to localStorage:', error);
        }
      }

      // Set up organization data for the org selector
      await fetchUserOrganizations(access_token, finalOrgId);

      console.log('Login successful for user:', user.email, {
        accessToken: access_token.substring(0, 20) + '...',
        refreshToken: refresh_token.substring(0, 20) + '...',
        orgId: finalOrgId,
        expiresIn: loginResponse.data.expires_in,
        timestamp: new Date().toISOString()
      });

      // Return success and redirect URL instead of redirecting directly
      return {
        success: true,
        redirectUrl: '/dashboard'
      };
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  // Login with tokens (for magic link and other token-based auth)
  const loginWithTokens = (accessToken: string, refreshToken: string, user: User, orgId?: string) => {
    console.log('Logging in with tokens for user:', user.email);

    // Validate tokens
    if (!accessToken || !refreshToken) {
      console.error('Login with tokens failed: Missing tokens');
      throw new Error('Access token and refresh token are required');
    }

    // Validate user
    if (!user || !user.email) {
      console.error('Login with tokens failed: Invalid user data');
      throw new Error('Valid user data is required');
    }

    // Use provided orgId or extract from user
    const finalOrgId = orgId || user.org_id || '';

    // Save to state and localStorage (only on client)
    setState({
      isAuthenticated: true,
      user,
      token: accessToken,
      refreshToken: refreshToken,
      orgId: finalOrgId,
      loading: false,
    });

    const storage = safeLocalStorage();
    if (storage) {
      try {
        storage.setItem('token', accessToken);
        storage.setItem('refreshToken', refreshToken);
        storage.setItem('orgId', finalOrgId);
        storage.setItem('user', JSON.stringify(user));
      } catch (error) {
        console.warn('Failed to save auth data to localStorage:', error);
      }
    }

    console.log('Login with tokens successful for user:', user.email, {
      accessToken: accessToken.substring(0, 20) + '...',
      refreshToken: refreshToken.substring(0, 20) + '...',
      orgId: finalOrgId,
      timestamp: new Date().toISOString()
    });
  };

  // Logout function
  const logout = async () => {
    console.log('Logout function called');

    try {
      // Call logout API if we have a token and orgId
      if (state.token) {
        console.log('Calling logout API with token:', state.token.substring(0, 10) + '...');

        try {
          await AuthAPI.logout(state.token, state.orgId || undefined);
          console.log('Logout API call successful');
        } catch (apiError) {
          console.error('Error during logout API call:', apiError);
          // Continue with local logout even if API call fails
        }
      } else {
        console.log('No token available, skipping API call');
      }
    } catch (error) {
      console.error('Error in logout function:', error);
      // Continue with local logout even if there's an error
    } finally {
      console.log('Clearing auth state');

      // Reset refresh-related variables
      refreshFailureCount = 0;
      isRefreshing = false;
      refreshSubscribers = [];

      // Clear state first
      setState({
        isAuthenticated: false,
        user: null,
        token: null,
        refreshToken: null,
        orgId: null,
        loading: false,
      });

      // Clear all items from localStorage (only on client)
      const storage = safeLocalStorage();
      if (storage) {
        console.log('Clearing localStorage');

        try {
          // Main auth items
          storage.removeItem('token');
          storage.removeItem('refreshToken');
          storage.removeItem('orgId');
          storage.removeItem('user');

          // Clear any other auth-related items
          const authKeys = ['userOrganizations', 'auth', 'session', 'next-auth.session-token', 'next-auth.callback-url', 'next-auth.csrf-token'];
          authKeys.forEach(key => {
            try {
              storage.removeItem(key);
            } catch (e) {
              console.warn(`Failed to remove ${key} from localStorage`, e);
            }
          });
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
      }

      // Clear session storage as well (only on client)
      if (isClient()) {
        try {
          sessionStorage.clear();
          console.log('Session storage cleared');
        } catch (e) {
          console.warn('Failed to clear session storage', e);
        }
      }

      console.log('Auth state cleared, redirecting to login page');

      // Use a small delay to ensure state is cleared before redirect
      setTimeout(() => {
        if (isClient()) {
          window.location.replace('/login');
        }
      }, 100);
    }
  };

  // Set current organization
  const setCurrentOrg = (orgId: string) => {
    setState(prev => ({ ...prev, orgId }));
    const storage = safeLocalStorage();
    if (storage) {
      try {
        storage.setItem('orgId', orgId);
      } catch (error) {
        console.warn('Failed to save orgId to localStorage:', error);
      }
    }
  };

  // Refresh authentication
  const refreshAuth = async (): Promise<boolean> => {
    if (!state.refreshToken) {
      console.error('No refresh token available');
      return false;
    }

    // Check if we've exceeded max refresh failures
    if (refreshFailureCount >= MAX_REFRESH_FAILURES) {
      console.error('Max refresh failures reached, forcing logout');
      logout();
      return false;
    }

    // If already refreshing, wait for the refresh to complete
    if (isRefreshing) {
      console.log('Token refresh already in progress, waiting...');
      return new Promise((resolve) => {
        subscribeTokenRefresh((token) => {
          resolve(!!token);
        });
      });
    }

    try {
      isRefreshing = true;
      console.log(`Attempting to refresh token (attempt ${refreshFailureCount + 1}/${MAX_REFRESH_FAILURES})`);

      // Call the refresh token API
      const refreshResponse = await AuthAPI.refreshToken(state.refreshToken);
      console.log('Refresh response received');

      const { access_token, refresh_token } = refreshResponse.data;

      if (!access_token || !refresh_token) {
        console.error('Invalid refresh response - missing tokens');
        refreshFailureCount++;
        return false;
      }

      // Extract X-ORG-ID from response headers (case-insensitive)
      const orgIdHeader = Object.keys(refreshResponse.headers)
        .find(key => key.toLowerCase() === 'x-org-id');

      const orgId = orgIdHeader ? refreshResponse.headers[orgIdHeader] : state.orgId;

      // Update state with new tokens (both access and refresh)
      setState(prev => ({
        ...prev,
        isAuthenticated: true,
        token: access_token,
        refreshToken: refresh_token,
        orgId: orgId || prev.orgId,
      }));

      // Update localStorage with both tokens
      const storage = safeLocalStorage();
      if (storage) {
        storage.setItem('token', access_token);
        storage.setItem('refreshToken', refresh_token);
        if (orgId) {
          storage.setItem('orgId', orgId);
        }
      }

      console.log('Token refresh successful', {
        newAccessToken: access_token.substring(0, 20) + '...',
        newRefreshToken: refresh_token.substring(0, 20) + '...',
        orgId,
        timestamp: new Date().toISOString()
      });

      // Notify all subscribers
      onRefreshComplete(access_token);
      return true;
    } catch (error: any) {
      console.error('Token refresh failed:', error);
      refreshFailureCount++;

      // Check if the error is a 401 Unauthorized or we've exceeded max failures
      if (error.response?.status === 401 || refreshFailureCount >= MAX_REFRESH_FAILURES) {
        console.log('Refresh token is invalid/expired or max failures reached, logging out');

        // Try to call logout API if we have a token
        if (state.token) {
          try {
            console.log('Calling logout API to invalidate tokens');
            await AuthAPI.logout(state.token, state.orgId || undefined);
            console.log('Logout API call successful');
          } catch (logoutError) {
            console.error('Error during logout API call:', logoutError);
            // Continue with logout even if API call fails
          }
        }

        // Notify subscribers of failure
        onRefreshComplete('');
        // Call logout to clear local state and redirect
        logout();
      }

      return false;
    } finally {
      isRefreshing = false;
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        loginWithTokens,
        logout,
        setCurrentOrg,
        refreshAuth,
        userOrganizations,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

// Custom hook to use the auth context
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
