/**
 * Investment Thesis API Integration
 *
 * This file contains API integration for the Investment Thesis Configuration feature.
 * It provides methods to interact with thesis, scoring rules, and match rules.
 */

import apiClient from '@/lib/api-client';

/**
 * Robust API call wrapper with error handling and validation
 */
async function safeApiCall<T>(
  operation: () => Promise<any>,
  operationName: string,
  validator?: (data: any) => boolean
): Promise<T> {
  try {
    console.log(`🔄 Starting ${operationName}`);
    const response = await operation();

    // Validate response structure
    if (!response || !response.data) {
      throw new Error(`Invalid response structure from ${operationName}`);
    }

    // Run custom validator if provided
    if (validator && !validator(response.data)) {
      throw new Error(`Response validation failed for ${operationName}`);
    }

    console.log(`✅ ${operationName} completed successfully`);
    return response.data;
  } catch (error: any) {
    console.error(`❌ ${operationName} failed:`, error);

    // Extract meaningful error message
    let errorMessage = `Failed to ${operationName.toLowerCase()}`;
    if (error.response?.data?.detail) {
      errorMessage = error.response.data.detail;
    } else if (error.message) {
      errorMessage = error.message;
    }

    // Re-throw with enhanced error information
    const enhancedError = new Error(errorMessage);
    (enhancedError as any).originalError = error;
    (enhancedError as any).operation = operationName;
    throw enhancedError;
  }
}
import {
  InvestmentThesis,
  ThesisWithRules,
  ThesisCreateRequest,
  ScoringRule,
  ScoringRuleCreateRequest,
  MatchRule,
  MatchRuleCreateRequest,
  BonusRule,
  BonusRuleCreateRequest,
  ThesisStatus,
  FilterCondition,
  CompoundFilter,
  RuleType
} from '@/lib/types/thesis';

/**
 * Thesis API service for interacting with the backend
 */
export const ThesisAPI = {
  /**
   * Create a new investment thesis
   * @param thesisData Thesis creation data
   * @returns Created thesis with rules
   */
  async createThesis(thesisData: ThesisCreateRequest): Promise<ThesisWithRules> {
    // Validate required fields
    if (!thesisData.name || !thesisData.form_id) {
      throw new Error('Thesis name and form_id are required');
    }

    // Prepare data for backend API
    const requestData = {
      name: thesisData.name,
      description: thesisData.description || '',
      form_id: thesisData.form_id,
      status: thesisData.status || 'draft',
      is_active: thesisData.is_active ?? true,
      scoring_rules: thesisData.scoring_rules || [],
      match_rules: thesisData.match_rules || []
    };

    return safeApiCall<ThesisWithRules>(
      () => apiClient.post('/thesis/', requestData),
      'create thesis',
      (data) => !!(data.id || data._id) // Validate thesis has an ID
    );
  },

  /**
   * Get a thesis by ID with all rules
   * @param thesisId Thesis ID
   * @returns Thesis with expanded rules
   */
  async getThesis(thesisId: string): Promise<ThesisWithRules> {
    if (!thesisId) {
      throw new Error('Thesis ID is required');
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.get(`/thesis/${thesisId}`),
      'get thesis',
      (data) => !!(data.id || data._id) && Array.isArray(data.scoring_rules) && Array.isArray(data.match_rules)
    );
  },

  /**
   * Update an existing thesis
   * @param thesisId Thesis ID
   * @param thesisData Thesis update data
   * @returns Updated thesis with rules
   */
  async updateThesis(thesisId: string, thesisData: Partial<ThesisCreateRequest>): Promise<ThesisWithRules> {
    if (!thesisId) {
      throw new Error('Thesis ID is required');
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.put(`/thesis/${thesisId}`, thesisData),
      'update thesis',
      (data) => !!(data.id || data._id)
    );
  },

  /**
   * List all theses with optional filtering
   * @param filters Optional filters
   * @returns List of theses
   */
  async listTheses(filters?: {
    form_id?: string;
    status?: ThesisStatus;
    is_active?: boolean;
    skip?: number;
    limit?: number;
  }): Promise<InvestmentThesis[]> {
    const params = new URLSearchParams();
    if (filters?.form_id) params.append('form_id', filters.form_id);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.is_active !== undefined) params.append('is_active', filters.is_active.toString());
    if (filters?.skip !== undefined) params.append('skip', filters.skip.toString());
    if (filters?.limit !== undefined) params.append('limit', filters.limit.toString());

    return safeApiCall<InvestmentThesis[]>(
      () => apiClient.get(`/thesis?${params.toString()}`),
      'list theses',
      (data) => Array.isArray(data)
    );
  },

  /**
   * Create a scoring rule for a thesis
   * @param thesisId Thesis ID
   * @param ruleData Scoring rule data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async createScoringRule(thesisId: string, ruleData: ScoringRuleCreateRequest): Promise<ThesisWithRules> {
    if (!thesisId) {
      throw new Error('Thesis ID is required');
    }

    // Validate required fields based on backend model
    if (!ruleData.question_id) {
      throw new Error('Question ID is required for scoring rules');
    }
    if (!ruleData.weight || ruleData.weight <= 0) {
      throw new Error('Weight must be a positive number');
    }
    if (!ruleData.condition) {
      throw new Error('Condition is required for all scoring rules');
    }

    // Validate condition structure based on type
    if ('operator' in ruleData.condition && 'value' in ruleData.condition) {
      // FilterCondition validation
      const condition = ruleData.condition as FilterCondition;
      if (!condition.question_id || !condition.operator || condition.value === undefined) {
        throw new Error('FilterCondition must have question_id, operator, and value');
      }
    } else if ('operator' in ruleData.condition && 'conditions' in ruleData.condition) {
      // CompoundFilter validation
      const condition = ruleData.condition as CompoundFilter;
      if (!condition.operator || !condition.conditions || condition.conditions.length === 0) {
        throw new Error('CompoundFilter must have operator and non-empty conditions array');
      }
    } else {
      throw new Error('Condition must be either FilterCondition or CompoundFilter');
    }

    // Ensure rule_type is set correctly
    const validatedRuleData = {
      ...ruleData,
      rule_type: RuleType.SCORING,
      thesis_id: thesisId
    };

    return safeApiCall<ThesisWithRules>(
      () => apiClient.post(`/thesis/${thesisId}/scoring-rules`, validatedRuleData),
      'create scoring rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.scoring_rules)
    );
  },

  /**
   * Update a scoring rule
   * @param ruleId Rule ID
   * @param ruleData Rule update data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async updateScoringRule(ruleId: string, ruleData: Partial<ScoringRuleCreateRequest>): Promise<ThesisWithRules> {
    if (!ruleId) {
      throw new Error('Rule ID is required');
    }

    // Validate fields if they are being updated
    if (ruleData.weight !== undefined && ruleData.weight <= 0) {
      throw new Error('Weight must be a positive number');
    }
    if (ruleData.condition) {
      // Validate condition structure based on type
      if ('operator' in ruleData.condition && 'value' in ruleData.condition) {
        // FilterCondition validation
        const condition = ruleData.condition as FilterCondition;
        if (!condition.question_id || !condition.operator || condition.value === undefined) {
          throw new Error('FilterCondition must have question_id, operator, and value');
        }
      } else if ('operator' in ruleData.condition && 'conditions' in ruleData.condition) {
        // CompoundFilter validation
        const condition = ruleData.condition as CompoundFilter;
        if (!condition.operator || !condition.conditions || condition.conditions.length === 0) {
          throw new Error('CompoundFilter must have operator and non-empty conditions array');
        }
      } else {
        throw new Error('Condition must be either FilterCondition or CompoundFilter');
      }
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.put(`/thesis/scoring-rules/${ruleId}`, ruleData),
      'update scoring rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.scoring_rules)
    );
  },

  /**
   * Delete a scoring rule
   * @param ruleId Rule ID
   * @returns Success message
   */
  async deleteScoringRule(ruleId: string): Promise<{ message: string }> {
    if (!ruleId) {
      throw new Error('Rule ID is required');
    }

    return safeApiCall<{ message: string }>(
      () => apiClient.delete(`/thesis/scoring-rules/${ruleId}`),
      'delete scoring rule',
      (data) => !!data.message
    );
  },

  /**
   * Create a match rule for a thesis
   * @param thesisId Thesis ID
   * @param ruleData Match rule data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async createMatchRule(thesisId: string, ruleData: MatchRuleCreateRequest): Promise<ThesisWithRules> {
    if (!thesisId) {
      throw new Error('Thesis ID is required');
    }
    if (!ruleData.name) {
      throw new Error('Rule name is required for match rules');
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.post(`/thesis/${thesisId}/match-rules`, ruleData),
      'create match rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.match_rules)
    );
  },

  /**
   * Update a match rule
   * @param ruleId Rule ID
   * @param ruleData Rule update data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async updateMatchRule(ruleId: string, ruleData: Partial<MatchRuleCreateRequest>): Promise<ThesisWithRules> {
    if (!ruleId) {
      throw new Error('Rule ID is required');
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.put(`/thesis/match-rules/${ruleId}`, ruleData),
      'update match rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.match_rules)
    );
  },

  /**
   * Delete a match rule
   * @param ruleId Rule ID
   * @returns Success message
   */
  async deleteMatchRule(ruleId: string): Promise<{ message: string }> {
    if (!ruleId) {
      throw new Error('Rule ID is required');
    }

    return safeApiCall<{ message: string }>(
      () => apiClient.delete(`/thesis/match-rules/${ruleId}`),
      'delete match rule',
      (data) => !!data.message
    );
  },

  /**
   * Create a bonus rule (same endpoint as scoring rules, different rule_type)
   * @param thesisId Thesis ID
   * @param ruleData Bonus rule data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async createBonusRule(thesisId: string, ruleData: BonusRuleCreateRequest): Promise<ThesisWithRules> {
    if (!thesisId) {
      throw new Error('Thesis ID is required');
    }

    // Validate required fields based on backend model
    if (!ruleData.bonus_points || ruleData.bonus_points <= 0) {
      throw new Error('Bonus points must be a positive number');
    }
    if (!ruleData.condition) {
      throw new Error('Condition is required for all bonus rules');
    }

    // Ensure rule_type is set correctly and weight is 1.0 for bonus rules
    const validatedRuleData = {
      ...ruleData,
      rule_type: RuleType.BONUS,
      weight: 1.0,
      thesis_id: thesisId
    };

    return safeApiCall<ThesisWithRules>(
      () => apiClient.post(`/thesis/${thesisId}/scoring-rules`, validatedRuleData),
      'create bonus rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.scoring_rules)
    );
  },

  /**
   * Update a bonus rule
   * @param ruleId Rule ID
   * @param ruleData Rule update data
   * @returns ThesisWithRules (backend returns full thesis with rules)
   */
  async updateBonusRule(ruleId: string, ruleData: Partial<BonusRuleCreateRequest>): Promise<ThesisWithRules> {
    if (!ruleId) {
      throw new Error('Rule ID is required');
    }

    return safeApiCall<ThesisWithRules>(
      () => apiClient.put(`/thesis/scoring-rules/${ruleId}`, ruleData),
      'update bonus rule',
      (data) => !!(data.id || data._id) && Array.isArray(data.scoring_rules)
    );
  },

  /**
   * Preview form questions with scoring configuration suggestions
   * @param formId Form ID
   * @param thesisId Optional thesis ID for existing configurations
   * @returns Form preview with suggestions
   */
  async previewFormQuestions(formId: string, thesisId?: string): Promise<any> {
    console.log(`Previewing form questions for form ${formId}, thesis: ${thesisId}`);

    const params = new URLSearchParams();
    if (thesisId) params.append('thesis_id', thesisId);
    params.append('include_suggestions', 'true');

    const response = await apiClient.get(`/thesis/preview/${formId}?${params.toString()}`);
    console.log('Form preview fetched:', response.data);
    return response.data;
  },

  /**
   * Find matching theses for form responses
   * @param data Form responses data
   * @returns Matching theses
   */
  async findMatchingTheses(data: { form_id: string; form_responses: any }): Promise<InvestmentThesis[]> {
    console.log('Finding matching theses for:', data);
    const response = await apiClient.post('/thesis/find-matching', data);
    console.log('Matching theses found:', response.data);
    return response.data;
  },

  /**
   * Delete a thesis
   * @param thesisId Thesis ID
   * @returns Success message
   */
  async deleteThesis(thesisId: string): Promise<{ success: boolean }> {
    console.log(`Deleting thesis ${thesisId}`);
    const response = await apiClient.delete(`/thesis/${thesisId}`);
    console.log('Thesis deleted:', response.data);
    return response.data;
  }
};

export default ThesisAPI;
