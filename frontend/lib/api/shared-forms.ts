// API client for shared forms

// Robust API base URL handling for production
const getApiBase = () => {
  if (typeof window !== 'undefined') {
    // Client-side: use environment variable or fallback
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
  }
  // Server-side: use environment variable or fallback
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
};

const API_BASE = getApiBase();

export interface SharedFormData {
  form: any;
  organization: any;
  sharing_config: any;
  branding?: any;
}

export interface SubmissionResponse {
  _id: string;
  form_id: string;
  org_id: string;
  answers: Record<string, any>;
  metadata?: Record<string, any>;
  status: string;
  created_at: number;
  updated_at: number;
}

export class SharedFormsAPI {
  static async getFormDetails(token: string): Promise<SharedFormData> {
    try {
      // Add timeout and better error handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`${API_BASE}/forms/share/${token}/details`, {
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('This form link has expired or is invalid.');
        }
        if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        }
        throw new Error('Failed to load form. Please try again.');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error('Request timed out. Please check your connection and try again.');
      }
      throw error;
    }
  }

  static async submitForm(token: string, answers: Record<string, any>, metadata?: Record<string, any>): Promise<SubmissionResponse> {
    const response = await fetch(`${API_BASE}/forms/share/${token}/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        answers,
        metadata: {
          ...metadata,
          submitted_at: new Date().toISOString(),
          user_agent: navigator.userAgent,
        },
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to submit form. Please try again.');
    }

    return response.json();
  }

  static async sendMagicLink(email: string, redirectUrl?: string): Promise<{ message: string }> {
    const response = await fetch(`${API_BASE}/magic-link`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        redirect_url: redirectUrl,
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to send magic link. Please try again.');
    }

    return response.json();
  }

  static async verifyMagicLink(token: string): Promise<{
    access_token: string;
    refresh_token: string;
    user: any;
    org_id: string;
  }> {
    const response = await fetch(`${API_BASE}/magic-link/verify/${token}`);

    if (!response.ok) {
      throw new Error('Invalid or expired magic link.');
    }

    return response.json();
  }

  static async saveProgress(token: string, userId: string, answers: Record<string, any>, accessToken?: string): Promise<void> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE}/forms/share/${token}/progress/${userId}`, {
      method: 'PUT',
      headers,
      body: JSON.stringify({
        answers,
        updated_at: new Date().toISOString(),
      }),
    });

    if (!response.ok) {
      throw new Error('Failed to save progress.');
    }
  }

  static async getProgress(token: string, userId: string, accessToken?: string): Promise<{ answers: Record<string, any> }> {
    const headers: Record<string, string> = {};

    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await fetch(`${API_BASE}/forms/share/${token}/progress/${userId}`, {
      headers,
    });

    if (!response.ok) {
      throw new Error('Failed to load progress.');
    }

    return response.json();
  }
}
