// API client for public submission flow

const getApiBase = () => {
  if (typeof window !== 'undefined') {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
  }
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';
};

const API_BASE = getApiBase();

export interface PublicLoginRequest {
  email: string;
  name?: string;
  token: string;
}

export interface PublicLoginResponse {
  message: string;
  email: string;
  requires_verification: boolean;
}

export interface MagicLinkVerifyResponse {
  access_token: string;
  refresh_token: string;
  user: {
    id: string;
    email: string;
    name?: string;
    type: 'public';
  };
  submission?: {
    id: string;
    status: string;
    progress: number;
    can_edit: boolean;
  };
  can_edit: boolean;
}

export interface PublicSubmission {
  id: string;
  status: 'draft' | 'submitted';
  progress: number;
  can_edit: boolean;
  answers: Record<string, any>;
  last_question?: string;
}

export interface SubmissionProgressRequest {
  answers: Record<string, any>;
  progress: number;
  last_question?: string;
}

export interface SubmissionProgressResponse {
  id: string;
  status: string;
  progress: number;
  message: string;
}

export interface SubmitResponse {
  id: string;
  status: string;
  message: string;
  public_submission_id: string;
}

export class PublicSubmissionAPI {
  /**
   * Login with email and sharing token
   */
  static async login(request: PublicLoginRequest): Promise<PublicLoginResponse> {
    try {
      const response = await fetch(`${API_BASE}/api/v1/public/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to send magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Public login error:', error);
      throw error;
    }
  }

  /**
   * Verify magic link token
   */
  static async verifyMagicLink(token: string): Promise<MagicLinkVerifyResponse> {
    try {
      const response = await fetch(`${API_BASE}/api/v1/public/magic-link/verify/${token}`);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Invalid or expired magic link');
      }

      return response.json();
    } catch (error) {
      console.error('Magic link verification error:', error);
      throw error;
    }
  }

  /**
   * Get submission by token and email
   */
  static async getSubmission(token: string, email: string): Promise<PublicSubmission> {
    try {
      const response = await fetch(
        `${API_BASE}/api/v1/public/submission/${token}?email=${encodeURIComponent(email)}`
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to get submission');
      }

      return response.json();
    } catch (error) {
      console.error('Get submission error:', error);
      throw error;
    }
  }

  /**
   * Save submission progress
   */
  static async saveProgress(
    submissionId: string,
    request: SubmissionProgressRequest,
    accessToken?: string
  ): Promise<SubmissionProgressResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/submission/${submissionId}/progress`, {
        method: 'POST',
        headers,
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to save progress');
      }

      return response.json();
    } catch (error) {
      console.error('Save progress error:', error);
      throw error;
    }
  }

  /**
   * Submit final submission
   */
  static async submitSubmission(
    submissionId: string,
    accessToken?: string
  ): Promise<SubmitResponse> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      if (accessToken) {
        headers['Authorization'] = `Bearer ${accessToken}`;
      }

      const response = await fetch(`${API_BASE}/api/v1/public/submission/${submissionId}/submit`, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || 'Failed to submit');
      }

      return response.json();
    } catch (error) {
      console.error('Submit submission error:', error);
      throw error;
    }
  }

  /**
   * Get sharing token details (form info, organization, etc.)
   */
  static async getTokenDetails(token: string): Promise<{
    form: any;
    organization: any;
    sharing_config: any;
    branding?: any;
  }> {
    try {
      const response = await fetch(`${API_BASE}/api/v1/forms/share/${token}/details`);

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('This form link has expired or is invalid.');
        }
        if (response.status >= 500) {
          throw new Error('Server error. Please try again later.');
        }
        throw new Error('Failed to load form details.');
      }

      return response.json();
    } catch (error) {
      console.error('Get token details error:', error);
      throw error;
    }
  }
}
