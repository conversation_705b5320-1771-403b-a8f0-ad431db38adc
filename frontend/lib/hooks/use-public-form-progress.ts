import { useState, useEffect, useCallback } from 'react';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';
import { PublicSubmissionAPI } from '@/lib/api/public-submission';
import { getVisibleQuestions, validateAllAnswers } from '@/lib/utils/form-validation';

// Debounce utility
function useDebounce<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const [debounceTimer, setDebounceTimer] = useState<NodeJS.Timeout | null>(null);

  const debouncedCallback = useCallback(
    (...args: Parameters<T>) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      const newTimer = setTimeout(() => {
        callback(...args);
      }, delay);

      setDebounceTimer(newTimer);
    },
    [callback, delay, debounceTimer]
  ) as T;

  useEffect(() => {
    return () => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }
    };
  }, [debounceTimer]);

  return debouncedCallback;
}

export function usePublicFormProgress(token: string, form: any) {
  const { isAuthenticated, user, accessToken, submission, updateSubmission } = usePublicAuth();
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [progress, setProgress] = useState(0);
  const [canSubmit, setCanSubmit] = useState(false);
  const [saving, setSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);

  // Mark as hydrated after first render
  useEffect(() => {
    setIsHydrated(true);
  }, []);

  // Load initial data from submission
  useEffect(() => {
    if (!isHydrated || !submission) return;

    if (submission.answers) {
      setAnswers(submission.answers);
    }
    setProgress(submission.progress || 0);
  }, [submission, isHydrated]);

  // Calculate progress and validation
  useEffect(() => {
    if (!form?.sections || !isHydrated) {
      return;
    }

    try {
      let totalRequired = 0;
      let totalAnswered = 0;

      // Build a map of sections for quick lookup
      const sectionMap: Record<string, any> = {};
      form.sections.forEach((section: any) => {
        if (section && (section._id || section.id)) {
          sectionMap[section._id || section.id] = section;
        }
      });

      // Get all visible questions based on current answers
      const visibleQuestions = getVisibleQuestions(form, answers);

      // Count required questions and answered questions
      visibleQuestions.forEach((question: any) => {
        if (question.required) {
          totalRequired++;
          
          const answer = answers[question._id || question.id];
          if (answer !== undefined && answer !== null && answer !== '') {
            // For arrays (multi-select), check if not empty
            if (Array.isArray(answer)) {
              if (answer.length > 0) {
                totalAnswered++;
              }
            } else {
              totalAnswered++;
            }
          }
        }
      });

      const progressPercent = totalRequired > 0 ? (totalAnswered / totalRequired) * 100 : 100;
      const canSubmitForm = totalRequired === 0 || totalAnswered === totalRequired;
      
      setProgress(Math.round(progressPercent));
      setCanSubmit(canSubmitForm);

      // Debug logging (only in development)
      if (process.env.NODE_ENV === 'development') {
        console.log('Public form progress calculation:', {
          totalRequired,
          totalAnswered,
          progressPercent: Math.round(progressPercent),
          canSubmit: canSubmitForm,
          answers: Object.keys(answers).length,
          submissionId: submission?.id
        });
      }
    } catch (error) {
      console.error('Error calculating progress:', error);
    }
  }, [answers, form, isHydrated, submission]);

  // Save progress to backend (debounced)
  const saveProgressToBackend = useCallback(async (
    answersToSave: Record<string, any>,
    progressToSave: number
  ) => {
    if (!isAuthenticated || !user || !submission?.id || !submission.can_edit) {
      return;
    }

    try {
      setSaving(true);
      
      await PublicSubmissionAPI.saveProgress(
        submission.id,
        {
          answers: answersToSave,
          progress: progressToSave,
        },
        accessToken || undefined
      );

      // Update local submission state
      updateSubmission({
        ...submission,
        answers: answersToSave,
        progress: progressToSave,
      });

      setLastSaved(new Date());
      console.log('Progress saved to backend:', { progress: progressToSave });
    } catch (error) {
      console.error('Failed to save progress to backend:', error);
    } finally {
      setSaving(false);
    }
  }, [isAuthenticated, user, submission, accessToken, updateSubmission]);

  // Debounced save function
  const debouncedSave = useDebounce(saveProgressToBackend, 2000); // 2 second delay

  // Auto-save when answers change
  useEffect(() => {
    if (!isHydrated || !isAuthenticated || !submission?.can_edit) {
      return;
    }

    // Only auto-save if we have answers and the form is loaded
    if (Object.keys(answers).length > 0 && form?.sections) {
      debouncedSave(answers, progress);
    }
  }, [answers, progress, isHydrated, isAuthenticated, submission, form, debouncedSave]);

  // Update answer function
  const updateAnswer = useCallback((questionId: string, value: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  }, []);

  // Remove answers for repeatable sections
  const removeRepeatAnswers = useCallback((sectionId: string, repeatIndex: number) => {
    setAnswers(prev => {
      const newAnswers = { ...prev };
      
      // Find all questions in this section and remove answers for the specific repeat index
      Object.keys(newAnswers).forEach(key => {
        if (key.startsWith(`${sectionId}_${repeatIndex}_`)) {
          delete newAnswers[key];
        }
      });
      
      return newAnswers;
    });
  }, []);

  // Manual save function
  const saveProgress = useCallback(async () => {
    if (!isAuthenticated || !submission?.id || !submission.can_edit) {
      return;
    }

    await saveProgressToBackend(answers, progress);
  }, [isAuthenticated, submission, answers, progress, saveProgressToBackend]);

  // Submit form function
  const submitForm = useCallback(async () => {
    if (!isAuthenticated || !submission?.id || !submission.can_edit || !canSubmit) {
      throw new Error('Cannot submit form');
    }

    try {
      // First save the latest progress
      await saveProgressToBackend(answers, progress);

      // Then submit the form
      const result = await PublicSubmissionAPI.submitSubmission(
        submission.id,
        accessToken || undefined
      );

      // Update submission status
      updateSubmission({
        ...submission,
        status: 'submitted',
        can_edit: false,
      });

      return result;
    } catch (error) {
      console.error('Failed to submit form:', error);
      throw error;
    }
  }, [
    isAuthenticated,
    submission,
    canSubmit,
    answers,
    progress,
    accessToken,
    saveProgressToBackend,
    updateSubmission
  ]);

  return {
    answers,
    updateAnswer,
    progress,
    canSubmit,
    saving,
    lastSaved,
    saveProgress,
    submitForm,
    removeRepeatAnswers,
    isAuthenticated,
    canEdit: submission?.can_edit || false,
    submissionStatus: submission?.status || 'draft',
  };
}
