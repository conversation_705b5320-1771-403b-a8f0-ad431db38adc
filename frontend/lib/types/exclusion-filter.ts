/**
 * Exclusion Filter Types
 *
 * TypeScript types for the Exclusion Filter feature, matching the backend models.
 */

// Supported operators for exclusion filter conditions
export enum ExclusionOperator {
  EQUALS = "eq",
  NOT_EQUALS = "ne", 
  GREATER_THAN = "gt",
  LESS_THAN = "lt",
  GREATER_THAN_OR_EQUAL = "gte",
  LESS_THAN_OR_EQUAL = "lte",
  IN = "in",
  NOT_IN = "not_in",
  CONTAINS = "contains",
  NOT_CONTAINS = "not_contains"
}

// Logical operators for combining conditions
export enum LogicalOperator {
  AND = "and",
  OR = "or"
}

// Individual condition within an exclusion filter
export interface ExclusionCondition {
  question_id: string;
  operator: ExclusionOperator;
  value: any; // Value type depends on question type and operator
}

// Exclusion filter model matching backend
export interface ExclusionFilter {
  _id?: string;
  id?: string;
  org_id: string;
  form_id: string;
  name: string;
  description?: string;
  operator: LogicalOperator; // How to combine conditions
  conditions: ExclusionCondition[];
  is_active: boolean;
  is_deleted: boolean;
  created_by: string;
  created_at: number;
  updated_at: number;
}

// Request models for API operations
export interface ExclusionFilterCreateRequest {
  form_id: string;
  name: string;
  description?: string;
  operator: LogicalOperator;
  conditions: ExclusionCondition[];
  is_active?: boolean;
}

export interface ExclusionFilterUpdateRequest {
  name?: string;
  description?: string;
  operator?: LogicalOperator;
  conditions?: ExclusionCondition[];
  is_active?: boolean;
}

// Response model for exclusion check
export interface ExclusionCheckRequest {
  form_id: string;
  answers: Record<string, any>;
}

export interface ExclusionCheckResponse {
  excluded: boolean;
  filter_id?: string;
  filter_name?: string;
  reason?: string;
}

// UI-specific types for the condition builder
export interface ConditionBuilderState {
  question_id: string;
  operator: ExclusionOperator;
  value: any;
  isValid: boolean;
  error?: string;
}

// Operator display information
export interface OperatorInfo {
  value: ExclusionOperator;
  label: string;
  symbol: string;
  description: string;
  supportedQuestionTypes: string[]; // Question types that support this operator
}

// Predefined operator configurations
export const OPERATOR_CONFIGS: OperatorInfo[] = [
  {
    value: ExclusionOperator.EQUALS,
    label: "equals",
    symbol: "=",
    description: "Exact match",
    supportedQuestionTypes: ["short_text", "long_text", "number", "single_select", "boolean", "date"]
  },
  {
    value: ExclusionOperator.NOT_EQUALS,
    label: "does not equal",
    symbol: "≠",
    description: "Not an exact match",
    supportedQuestionTypes: ["short_text", "long_text", "number", "single_select", "boolean", "date"]
  },
  {
    value: ExclusionOperator.GREATER_THAN,
    label: "greater than",
    symbol: ">",
    description: "Numeric comparison",
    supportedQuestionTypes: ["number", "range", "date"]
  },
  {
    value: ExclusionOperator.LESS_THAN,
    label: "less than", 
    symbol: "<",
    description: "Numeric comparison",
    supportedQuestionTypes: ["number", "range", "date"]
  },
  {
    value: ExclusionOperator.GREATER_THAN_OR_EQUAL,
    label: "greater than or equal",
    symbol: "≥",
    description: "Numeric comparison",
    supportedQuestionTypes: ["number", "range", "date"]
  },
  {
    value: ExclusionOperator.LESS_THAN_OR_EQUAL,
    label: "less than or equal",
    symbol: "≤",
    description: "Numeric comparison", 
    supportedQuestionTypes: ["number", "range", "date"]
  },
  {
    value: ExclusionOperator.IN,
    label: "is one of",
    symbol: "∈",
    description: "Value is in a list",
    supportedQuestionTypes: ["single_select", "multi_select"]
  },
  {
    value: ExclusionOperator.NOT_IN,
    label: "is not one of",
    symbol: "∉",
    description: "Value is not in a list",
    supportedQuestionTypes: ["single_select", "multi_select"]
  },
  {
    value: ExclusionOperator.CONTAINS,
    label: "contains",
    symbol: "⊃",
    description: "Text contains substring",
    supportedQuestionTypes: ["short_text", "long_text", "multi_select"]
  },
  {
    value: ExclusionOperator.NOT_CONTAINS,
    label: "does not contain",
    symbol: "⊅",
    description: "Text does not contain substring",
    supportedQuestionTypes: ["short_text", "long_text", "multi_select"]
  }
];

// Helper function to get supported operators for a question type
export function getSupportedOperators(questionType: string): OperatorInfo[] {
  return OPERATOR_CONFIGS.filter(config => 
    config.supportedQuestionTypes.includes(questionType)
  );
}

// Helper function to get operator info by value
export function getOperatorInfo(operator: ExclusionOperator): OperatorInfo | undefined {
  return OPERATOR_CONFIGS.find(config => config.value === operator);
}
