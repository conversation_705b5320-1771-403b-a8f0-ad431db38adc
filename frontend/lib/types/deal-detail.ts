// Extended types for deal detail page
import { Deal } from './deal';

export interface TimelineEvent {
  id: string;
  date: string;
  event: string;
  description?: string;
  user_id?: string;
  user_name?: string;
  type: 'system' | 'user' | 'score' | 'document' | 'status';
  metadata?: Record<string, any>;
}

export interface ScoreBreakdown {
  overall_score: number;
  signals: {
    team_strength: {
      score: number;
      explanation: string;
      details?: Record<string, any>;
    };
    market_signals: {
      score: number;
      explanation: string;
      details?: Record<string, any>;
    };
    thesis_match: {
      score: number;
      explanation: string;
      details?: Record<string, any>;
    };
  };
  last_updated: string;
}

export interface Founder {
  id: string;
  name: string;
  title: string;
  photo_url?: string;
  linkedin_url?: string;
  twitter_url?: string;
  github_url?: string;
  bio?: string;
  previous_companies?: string[];
  badges?: string[];
  score?: number;
}

export interface ExternalSignal {
  id: string;
  headline: string;
  summary: string;
  source: string;
  source_url: string;
  date: string;
  type: 'news' | 'funding' | 'product' | 'hiring' | 'partnership';
  sentiment?: 'positive' | 'neutral' | 'negative';
}

export interface DealDocument {
  id: string;
  name: string;
  type: 'pdf' | 'doc' | 'xls' | 'ppt' | 'image' | 'other';
  size: number;
  uploaded_by: string;
  uploaded_by_name?: string;
  uploaded_at: string;
  url: string;
  preview_url?: string;
  can_delete?: boolean;
}

export interface ChatMessage {
  id: string;
  message: string;
  response: string;
  timestamp: string;
  user_id: string;
}

export interface DealDetailData extends Deal {
  timeline: TimelineEvent[];
  score_breakdown?: ScoreBreakdown;
  founders: Founder[];
  external_signals: ExternalSignal[];
  documents: DealDocument[];
  chat_history: ChatMessage[];
}

// Mock data for development
export const mockTimelineEvents: TimelineEvent[] = [
  {
    id: '1',
    date: '2024-01-15T10:30:00Z',
    event: 'Deal Created',
    description: 'Initial submission received through website form',
    type: 'system',
    user_name: 'System'
  },
  {
    id: '2',
    date: '2024-01-15T11:15:00Z',
    event: 'Deal Scored',
    description: 'Automated scoring completed with overall score of 85',
    type: 'score',
    user_name: 'AI Scoring Engine'
  },
  {
    id: '3',
    date: '2024-01-16T09:20:00Z',
    event: 'Document Uploaded',
    description: 'Pitch deck uploaded by founder',
    type: 'document',
    user_name: 'John Doe'
  },
  {
    id: '4',
    date: '2024-01-16T14:45:00Z',
    event: 'Status Updated',
    description: 'Status changed from "New" to "Active"',
    type: 'user',
    user_name: 'Sarah Wilson'
  }
];

export const mockScoreBreakdown: ScoreBreakdown = {
  overall_score: 85,
  signals: {
    team_strength: {
      score: 90,
      explanation: 'Strong founding team with relevant experience in fintech and previous successful exits.'
    },
    market_signals: {
      score: 82,
      explanation: 'Growing market with positive sentiment and recent funding activity in the sector.'
    },
    thesis_match: {
      score: 83,
      explanation: 'Good alignment with investment thesis, particularly in B2B SaaS and AI automation.'
    }
  },
  last_updated: '2024-01-15T11:15:00Z'
};

export const mockFounders: Founder[] = [
  {
    id: '1',
    name: 'John Doe',
    title: 'CEO & Co-Founder',
    linkedin_url: 'https://linkedin.com/in/johndoe',
    twitter_url: 'https://twitter.com/johndoe',
    bio: 'Former VP of Engineering at Stripe, 10+ years in fintech',
    previous_companies: ['Stripe', 'Square'],
    badges: ['2x Founder', 'YC Alum', 'Top 1% Network'],
    score: 92
  },
  {
    id: '2',
    name: 'Jane Smith',
    title: 'CTO & Co-Founder',
    linkedin_url: 'https://linkedin.com/in/janesmith',
    github_url: 'https://github.com/janesmith',
    bio: 'Former Senior Engineer at Google, AI/ML specialist',
    previous_companies: ['Google', 'DeepMind'],
    badges: ['AI Expert', 'Stanford PhD'],
    score: 88
  }
];

export const mockExternalSignals: ExternalSignal[] = [
  {
    id: '1',
    headline: 'Acme Corp raises $10M Series A to expand AI automation platform',
    summary: 'The company plans to use the funding to expand its AI automation platform and hire additional engineering talent.',
    source: 'TechCrunch',
    source_url: 'https://techcrunch.com/acme-corp-series-a',
    date: '2024-01-10T08:00:00Z',
    type: 'funding',
    sentiment: 'positive'
  },
  {
    id: '2',
    headline: 'AI automation market expected to grow 25% annually through 2028',
    summary: 'Industry report shows strong growth trajectory for AI automation solutions in enterprise markets.',
    source: 'Forbes',
    source_url: 'https://forbes.com/ai-automation-growth',
    date: '2024-01-08T12:30:00Z',
    type: 'news',
    sentiment: 'positive'
  }
];

export const mockDocuments: DealDocument[] = [
  {
    id: '1',
    name: 'Pitch Deck - Series A.pdf',
    type: 'pdf',
    size: 2500000,
    uploaded_by: 'founder_1',
    uploaded_by_name: 'John Doe',
    uploaded_at: '2024-01-16T09:20:00Z',
    url: '/api/documents/1/download',
    can_delete: false
  },
  {
    id: '2',
    name: 'Financial Model Q4 2023.xlsx',
    type: 'xls',
    size: 850000,
    uploaded_by: 'founder_2',
    uploaded_by_name: 'Jane Smith',
    uploaded_at: '2024-01-16T10:15:00Z',
    url: '/api/documents/2/download',
    can_delete: false
  }
];
