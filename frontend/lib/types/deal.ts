// Deal types and interfaces for the frontend

export interface Deal {
  id: string;
  org_id: string;
  form_id: string;
  submission_ids: string[];
  
  // Core fields
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  
  // Deal tracking fields
  status: DealStatus;
  exclusion_filter_result?: {
    excluded: boolean;
    reason?: string;
  };
  scoring?: {
    total_score: number;
    normalized_score: number;
    thesis_matches?: string[];
  };
  
  // Additional fields
  notes?: string;
  tags?: string[];
  timeline?: TimelineEvent[];
  
  // Metadata
  created_by: string;
  created_at: number;
  updated_at: number;
}

export enum DealStatus {
  NEW = "new",
  TRIAGE = "triage", 
  ACTIVE = "active",
  COMPLETED = "completed",
  FLAGGED = "flagged",
  HARD_PASS = "hard_pass"
}

export interface TimelineEvent {
  date: string;
  event: string;
  notes?: string;
}

export interface DealListResponse {
  deals: Deal[];
  total: number;
  skip: number;
  limit: number;
  has_more: boolean;
}

export interface DealCreateRequest {
  org_id: string;
  form_id: string;
  submission_id: string;
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

export interface DealUpdateRequest {
  company_name?: string;
  stage?: string;
  sector?: string | string[];
  status?: DealStatus;
  notes?: string;
  tags?: string[];
}

// UI-specific types
export interface DealCardData {
  id: string;
  company_name: string;
  stage: string;
  sector: string;
  description: string;
  source: string;
  country: string;
  score: number;
  status: DealStatus;
  avatar_color: string;
  initials: string;
}

export interface DealFilters {
  status?: DealStatus;
  stage?: string;
  sector?: string;
  search?: string;
  tags?: string[];
}

// Mock data types for demo
export interface MockDealData extends DealCardData {
  created_at: number;
  updated_at: number;
}
