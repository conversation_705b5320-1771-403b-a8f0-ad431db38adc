import axios from 'axios';

// Create an Axios instance with default configuration
const apiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Important for handling cookies/authentication
});

// Log the API URL being used for debugging
console.log('API Client using baseURL:', process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1');

// Helper to get tokens and org ID
function getAccessToken() {
  return localStorage.getItem('token');
}
function getRefreshToken() {
  return localStorage.getItem('refreshToken');
}
function getOrgId() {
  return localStorage.getItem('orgId');
}

// Helper to store new tokens
function storeTokens({ accessToken, refreshToken }: { accessToken: string, refreshToken: string }) {
  localStorage.setItem('token', accessToken);
  localStorage.setItem('refreshToken', refreshToken);
}

// Helper to logout
function logoutAndRedirect() {
  localStorage.removeItem('token');
  localStorage.removeItem('refreshToken');
  window.location.href = '/login';
}

// Request interceptor: always attach token and org ID
apiClient.interceptors.request.use(config => {
  const token = getAccessToken();
  const orgId = getOrgId();

  if (!config.headers) config.headers = {};

  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  if (orgId) {
    config.headers['X-ORG-ID'] = orgId;
  }

  // Debug logging for API requests
  console.log('🌐 API Request:', {
    url: config.url,
    method: config.method,
    hasToken: !!token,
    hasOrgId: !!orgId,
    orgId: orgId,
    tokenPreview: token ? token.substring(0, 20) + '...' : 'none'
  });

  return config;
});

// Global refresh state to prevent race conditions
let isRefreshing = false;
let failedQueue: Array<{
  resolve: (value: any) => void;
  reject: (error: any) => void;
}> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

// Response interceptor: handle 401s with token refresh
apiClient.interceptors.response.use(
  response => response,
  async error => {
    const originalRequest = error.config;

    // Log API errors for debugging
    if (error.response) {
      console.log('API Error:', {
        url: error.config?.url,
        method: error.config?.method,
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      });
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401 && !originalRequest._retry) {
      console.log('🔄 401 detected, attempting token refresh...');

      // If already refreshing, queue this request
      if (isRefreshing) {
        console.log('⏳ Refresh in progress, queueing request...');
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          console.log('❌ No refresh token available, logging out');
          processQueue(error, null);
          // Clear auth state and redirect to login
          localStorage.clear();
          window.location.href = '/login';
          return Promise.reject(error);
        }

        console.log('🔄 Calling refresh token API...');

        // Import AuthAPI dynamically to avoid circular dependencies
        const { default: AuthAPI } = await import('./api/auth-api');
        const refreshResponse = await AuthAPI.refreshToken(refreshToken);

        const { access_token, refresh_token } = refreshResponse.data;

        if (!access_token || !refresh_token) {
          throw new Error('Invalid refresh response');
        }

        // Update stored tokens
        localStorage.setItem('token', access_token);
        localStorage.setItem('refreshToken', refresh_token);

        console.log('✅ Token refresh successful');

        // Update the failed request with new token
        originalRequest.headers.Authorization = `Bearer ${access_token}`;

        // Process queued requests
        processQueue(null, access_token);

        // Retry the original request
        return apiClient(originalRequest);

      } catch (refreshError: any) {
        console.error('❌ Token refresh failed:', refreshError);

        // Process queued requests with error
        processQueue(refreshError, null);

        // Clear auth state and redirect to login
        localStorage.clear();
        window.location.href = '/login';

        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient;
