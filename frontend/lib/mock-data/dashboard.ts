// Mock data for dashboard components
// TODO: Replace with real API calls when backend is ready

export interface DashboardStats {
  activeDeals: number;
  dealsTrend: number;
  forms: number;
  theses: number;
  aiActivity: {
    status: 'active' | 'inactive';
    lastSync: string;
  };
}

export interface SectorData {
  name: string;
  value: number;
  color: string;
}

export interface DealStageData {
  stage: string;
  count: number;
  color: string;
}

export interface ScoreDistributionData {
  range: string;
  count: number;
  color: string;
}

export interface ActivityItem {
  id: string;
  type: 'ai_processed' | 'new_deal' | 'new_thesis' | 'form_submission';
  title: string;
  description: string;
  timestamp: string;
  icon: string;
}

// Mock dashboard statistics
export const mockDashboardStats: DashboardStats = {
  activeDeals: 10,
  dealsTrend: 12, // +12% this month
  forms: 7,
  theses: 3,
  aiActivity: {
    status: 'active',
    lastSync: '30s ago'
  }
};

// Mock sector distribution data
export const mockSectorData: SectorData[] = [
  { name: 'FinTech', value: 25, color: '#3b82f6' },
  { name: 'HealthTech', value: 20, color: '#10b981' },
  { name: 'EdTech', value: 15, color: '#8b5cf6' },
  { name: 'E-commerce', value: 12, color: '#f59e0b' },
  { name: 'SaaS', value: 10, color: '#06b6d4' },
  { name: 'AI/ML', value: 8, color: '#ec4899' },
  { name: 'CleanTech', value: 5, color: '#84cc16' },
  { name: 'PropTech', value: 3, color: '#f97316' },
  { name: 'Other', value: 2, color: '#6b7280' }
];

// Mock deal stages data
export const mockDealStagesData: DealStageData[] = [
  { stage: 'Pre-Seed', count: 4, color: '#3b82f6' },
  { stage: 'Seed', count: 3, color: '#10b981' },
  { stage: 'Series A', count: 2, color: '#8b5cf6' },
  { stage: 'Series B', count: 1, color: '#f59e0b' }
];

// Mock score distribution data
export const mockScoreDistributionData: ScoreDistributionData[] = [
  { range: '90-100', count: 2, color: '#10b981' },
  { range: '80-89', count: 3, color: '#3b82f6' },
  { range: '70-79', count: 3, color: '#8b5cf6' },
  { range: '60-69', count: 1, color: '#f59e0b' },
  { range: '50-59', count: 1, color: '#ef4444' }
];

// Mock recent activity data
export const mockActivityData: ActivityItem[] = [
  {
    id: '1',
    type: 'ai_processed',
    title: 'AI processed new lead',
    description: 'TechCorp startup automatically scored and categorized',
    timestamp: '2 minutes ago',
    icon: 'brain'
  },
  {
    id: '2',
    type: 'new_deal',
    title: 'New deal added',
    description: 'HealthTech startup "MedFlow" added to pipeline',
    timestamp: '15 minutes ago',
    icon: 'plus'
  },
  {
    id: '3',
    type: 'new_thesis',
    title: 'Investment thesis updated',
    description: 'FinTech thesis criteria refined for Q1 2024',
    timestamp: '1 hour ago',
    icon: 'file'
  },
  {
    id: '4',
    type: 'form_submission',
    title: 'Form submission received',
    description: 'Startup application completed via shared form',
    timestamp: '2 hours ago',
    icon: 'form'
  },
  {
    id: '5',
    type: 'ai_processed',
    title: 'Batch processing completed',
    description: '12 startups analyzed and scored automatically',
    timestamp: '3 hours ago',
    icon: 'brain'
  },
  {
    id: '6',
    type: 'new_deal',
    title: 'Deal stage updated',
    description: 'EduTech startup moved to Series A evaluation',
    timestamp: '4 hours ago',
    icon: 'arrow-up'
  }
];

// Mock sharing data
export const mockSharingData = {
  qrCodeUrl: 'https://traction-x.app/submit/1',
  shareUrl: 'https://traction-x.app/submit/1',
  formTitle: 'Investment Application Form'
};

// Onboarding steps
export const onboardingSteps = [
  {
    id: 1,
    title: 'Welcome to TractionX',
    description: 'Your AI-powered investment intelligence platform',
    icon: 'welcome'
  },
  {
    id: 2,
    title: 'Setup Your Investment Thesis',
    description: 'Define your investment criteria and scoring rules',
    icon: 'target',
    action: 'Create Thesis',
    href: '/theses'
  },
  {
    id: 3,
    title: 'Create Application Forms',
    description: 'Build forms to collect startup information',
    icon: 'form',
    action: 'Create Form',
    href: '/forms'
  },
  {
    id: 4,
    title: 'Share & Start Collecting',
    description: 'Share your forms and let AI score submissions',
    icon: 'share',
    action: 'Share Forms',
    href: '/forms'
  }
];
