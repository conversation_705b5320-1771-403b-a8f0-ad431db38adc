// Mock deal data for demo purposes
// TODO: Replace with real API calls when backend is ready

import { MockDealData, DealStatus } from '@/lib/types/deal';

// Pastel color palette for avatars
const avatarColors = [
  'bg-blue-100 text-blue-700',
  'bg-green-100 text-green-700',
  'bg-purple-100 text-purple-700',
  'bg-pink-100 text-pink-700',
  'bg-yellow-100 text-yellow-700',
  'bg-indigo-100 text-indigo-700',
  'bg-red-100 text-red-700',
  'bg-orange-100 text-orange-700',
  'bg-teal-100 text-teal-700',
  'bg-cyan-100 text-cyan-700'
];

// Helper function to generate initials
const getInitials = (companyName: string): string => {
  return companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
};

// Helper function to get random avatar color
const getRandomAvatarColor = (): string => {
  return avatarColors[Math.floor(Math.random() * avatarColors.length)];
};

// Mock deals data
export const mockDeals: MockDealData[] = [
  {
    id: '1',
    company_name: 'TechFlow AI',
    stage: 'Seed',
    sector: 'AI/ML',
    description: 'Revolutionary AI platform for automated workflow optimization in enterprise environments.',
    source: 'Direct',
    country: 'USA',
    score: 92,
    status: DealStatus.ACTIVE,
    avatar_color: 'bg-blue-100 text-blue-700',
    initials: 'TF',
    created_at: Date.now() - 86400000 * 2, // 2 days ago
    updated_at: Date.now() - 3600000 // 1 hour ago
  },
  {
    id: '2',
    company_name: 'HealthTech Solutions',
    stage: 'Series A',
    sector: 'HealthTech',
    description: 'Digital health platform connecting patients with specialized care providers globally.',
    source: 'Network',
    country: 'Canada',
    score: 88,
    status: DealStatus.TRIAGE,
    avatar_color: 'bg-green-100 text-green-700',
    initials: 'HS',
    created_at: Date.now() - 86400000 * 5, // 5 days ago
    updated_at: Date.now() - 7200000 // 2 hours ago
  },
  {
    id: '3',
    company_name: 'EduVerse',
    stage: 'Pre-Seed',
    sector: 'EdTech',
    description: 'Immersive VR learning platform for K-12 education with personalized curricula.',
    source: 'Crunchbase',
    country: 'UK',
    score: 85,
    status: DealStatus.NEW,
    avatar_color: 'bg-purple-100 text-purple-700',
    initials: 'EV',
    created_at: Date.now() - 86400000 * 1, // 1 day ago
    updated_at: Date.now() - 1800000 // 30 minutes ago
  },
  {
    id: '4',
    company_name: 'GreenEnergy Co',
    stage: 'Series B',
    sector: 'CleanTech',
    description: 'Next-generation solar panel technology with 40% higher efficiency rates.',
    source: 'Direct',
    country: 'Germany',
    score: 94,
    status: DealStatus.ACTIVE,
    avatar_color: 'bg-emerald-100 text-emerald-700',
    initials: 'GE',
    created_at: Date.now() - 86400000 * 7, // 1 week ago
    updated_at: Date.now() - 900000 // 15 minutes ago
  },
  {
    id: '5',
    company_name: 'FinFlow',
    stage: 'Seed',
    sector: 'FinTech',
    description: 'AI-powered personal finance management with predictive spending insights.',
    source: 'Network',
    country: 'Singapore',
    score: 79,
    status: DealStatus.FLAGGED,
    avatar_color: 'bg-yellow-100 text-yellow-700',
    initials: 'FF',
    created_at: Date.now() - 86400000 * 3, // 3 days ago
    updated_at: Date.now() - 5400000 // 1.5 hours ago
  },
  {
    id: '6',
    company_name: 'PropTech Innovations',
    stage: 'Series A',
    sector: 'PropTech',
    description: 'Smart building management system with IoT integration and energy optimization.',
    source: 'Website',
    country: 'Thailand',
    score: 87,
    status: DealStatus.COMPLETED,
    avatar_color: 'bg-indigo-100 text-indigo-700',
    initials: 'PI',
    created_at: Date.now() - 86400000 * 14, // 2 weeks ago
    updated_at: Date.now() - 86400000 * 2 // 2 days ago
  },
  {
    id: '7',
    company_name: 'CyberShield',
    stage: 'Pre-Seed',
    sector: 'Cybersecurity',
    description: 'Advanced threat detection using machine learning for enterprise security.',
    source: 'Crunchbase',
    country: 'Israel',
    score: 91,
    status: DealStatus.ACTIVE,
    avatar_color: 'bg-red-100 text-red-700',
    initials: 'CS',
    created_at: Date.now() - 86400000 * 4, // 4 days ago
    updated_at: Date.now() - 2700000 // 45 minutes ago
  },
  {
    id: '8',
    company_name: 'FoodTech Labs',
    stage: 'Seed',
    sector: 'FoodTech',
    description: 'Lab-grown meat production technology for sustainable protein alternatives.',
    source: 'Network',
    country: 'Netherlands',
    score: 83,
    status: DealStatus.TRIAGE,
    avatar_color: 'bg-orange-100 text-orange-700',
    initials: 'FL',
    created_at: Date.now() - 86400000 * 6, // 6 days ago
    updated_at: Date.now() - 10800000 // 3 hours ago
  },
  {
    id: '9',
    company_name: 'SpaceLogistics',
    stage: 'Series A',
    sector: 'Aerospace',
    description: 'Satellite-based logistics tracking for global supply chain optimization.',
    source: 'Direct',
    country: 'USA',
    score: 89,
    status: DealStatus.NEW,
    avatar_color: 'bg-teal-100 text-teal-700',
    initials: 'SL',
    created_at: Date.now() - 86400000 * 8, // 8 days ago
    updated_at: Date.now() - 14400000 // 4 hours ago
  },
  {
    id: '10',
    company_name: 'BioMed Analytics',
    stage: 'Pre-Seed',
    sector: 'BioTech',
    description: 'AI-driven drug discovery platform accelerating pharmaceutical research.',
    source: 'Crunchbase',
    country: 'Switzerland',
    score: 86,
    status: DealStatus.ACTIVE,
    avatar_color: 'bg-cyan-100 text-cyan-700',
    initials: 'BA',
    created_at: Date.now() - 86400000 * 9, // 9 days ago
    updated_at: Date.now() - 7200000 // 2 hours ago
  }
];

// Filter options for the UI
export const stageOptions = [
  'All Stages',
  'Pre-Seed',
  'Seed',
  'Series A',
  'Series B',
  'Series C+'
];

export const sectorOptions = [
  'All Sectors',
  'AI/ML',
  'HealthTech',
  'EdTech',
  'FinTech',
  'CleanTech',
  'PropTech',
  'Cybersecurity',
  'FoodTech',
  'Aerospace',
  'BioTech'
];

export const statusOptions = [
  { value: 'all', label: 'All' },
  { value: DealStatus.NEW, label: 'New' },
  { value: DealStatus.TRIAGE, label: 'Triage' },
  { value: DealStatus.ACTIVE, label: 'Active' },
  { value: DealStatus.COMPLETED, label: 'Completed' },
  { value: DealStatus.FLAGGED, label: 'Flagged' }
];

// Helper functions
export const getStageColor = (stage: string): string => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-gray-100 text-gray-700 border-gray-200';
    case 'seed':
      return 'bg-green-100 text-green-700 border-green-200';
    case 'series a':
      return 'bg-blue-100 text-blue-700 border-blue-200';
    case 'series b':
      return 'bg-purple-100 text-purple-700 border-purple-200';
    case 'series c':
    case 'series c+':
      return 'bg-indigo-100 text-indigo-700 border-indigo-200';
    default:
      return 'bg-gray-100 text-gray-700 border-gray-200';
  }
};
