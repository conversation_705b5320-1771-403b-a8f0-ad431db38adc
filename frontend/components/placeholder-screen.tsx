import Image from "next/image"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

interface PlaceholderScreenProps {
  title: string
  description: string
  imagePath: string
  buttonText?: string
  buttonLink?: string
}

export function PlaceholderScreen({
  title,
  description,
  imagePath,
  buttonText = "Go to Dashboard",
  buttonLink = "/dashboard",
}: PlaceholderScreenProps) {
  return (
    <div className="flex min-h-[80vh] flex-col items-center justify-center p-4 text-center">
      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <CardTitle className="text-3xl font-bold">{title}</CardTitle>
          <CardDescription className="text-lg">{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="relative mx-auto aspect-video w-full max-w-2xl overflow-hidden rounded-lg">
            <Image
              src={imagePath}
              alt="Coming Soon"
              fill
              className="object-cover"
              priority
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-center">
          <Button asChild size="lg">
            <Link href={buttonLink}>{buttonText}</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
}
