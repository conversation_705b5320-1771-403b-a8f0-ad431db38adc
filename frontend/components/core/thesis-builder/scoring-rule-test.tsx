"use client"

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle, XCircle, Play, RefreshCw } from 'lucide-react';
import { 
  ScoringRule, 
  RuleType, 
  ConditionOperator, 
  FilterCondition 
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { RuleEditor } from './rule-editor';
import { toast } from '@/components/ui/use-toast';

interface ScoringRuleTestProps {
  onCreateScoringRule?: (questionId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onUpdateScoringRule?: (ruleId: string, rule: Partial<ScoringRule>) => Promise<void>;
  onDeleteScoringRule?: (ruleId: string) => Promise<void>;
}

interface TestResult {
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
}

export function ScoringRuleTest({
  onCreateScoringRule,
  onUpdateScoringRule,
  onDeleteScoringRule
}: ScoringRuleTestProps) {
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [showRuleEditor, setShowRuleEditor] = useState(false);
  const [testRule, setTestRule] = useState<Partial<ScoringRule> | undefined>();

  const mockQuestionId = 'test-question-123';
  const mockQuestionLabel = 'Test Question for Scoring Rule';

  const runTests = async () => {
    if (!onCreateScoringRule || !onUpdateScoringRule || !onDeleteScoringRule) {
      toast({
        title: 'Cannot run tests',
        description: 'API functions are not properly wired',
        variant: 'destructive'
      });
      return;
    }

    setIsRunning(true);
    const results: TestResult[] = [];

    // Test 1: Create a scoring rule
    results.push({ test: 'Create Scoring Rule', status: 'pending', message: 'Starting...' });
    setTestResults([...results]);

    try {
      const testRuleData: Partial<ScoringRule> = {
        rule_type: RuleType.SCORING,
        question_id: mockQuestionId,
        weight: 2.5,
        condition: {
          question_id: mockQuestionId,
          operator: ConditionOperator.EQUALS,
          value: 'test_value'
        } as FilterCondition
      };

      console.log('🧪 Test 1: Creating scoring rule:', testRuleData);
      await onCreateScoringRule(mockQuestionId, testRuleData);
      
      results[results.length - 1] = {
        test: 'Create Scoring Rule',
        status: 'success',
        message: 'Successfully created scoring rule',
        details: `Rule created for question ${mockQuestionId} with weight 2.5`
      };
      setTestResults([...results]);

      // Wait a moment for state to update
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Test 2: Update the scoring rule (simulate updating weight)
      results.push({ test: 'Update Scoring Rule', status: 'pending', message: 'Starting...' });
      setTestResults([...results]);

      try {
        // For this test, we'll use a mock rule ID since we don't have the actual created rule ID
        const mockRuleId = 'test-rule-id';
        const updateData: Partial<ScoringRule> = {
          weight: 3.0
        };

        console.log('🧪 Test 2: Updating scoring rule:', mockRuleId, updateData);
        await onUpdateScoringRule(mockRuleId, updateData);
        
        results[results.length - 1] = {
          test: 'Update Scoring Rule',
          status: 'success',
          message: 'Successfully updated scoring rule',
          details: 'Updated weight from 2.5 to 3.0'
        };
        setTestResults([...results]);

        // Test 3: Delete the scoring rule
        results.push({ test: 'Delete Scoring Rule', status: 'pending', message: 'Starting...' });
        setTestResults([...results]);

        try {
          console.log('🧪 Test 3: Deleting scoring rule:', mockRuleId);
          await onDeleteScoringRule(mockRuleId);
          
          results[results.length - 1] = {
            test: 'Delete Scoring Rule',
            status: 'success',
            message: 'Successfully deleted scoring rule',
            details: `Removed rule ${mockRuleId}`
          };
          setTestResults([...results]);
        } catch (error) {
          console.error('🧪 Test 3 failed:', error);
          results[results.length - 1] = {
            test: 'Delete Scoring Rule',
            status: 'error',
            message: 'Failed to delete scoring rule',
            details: error instanceof Error ? error.message : 'Unknown error'
          };
          setTestResults([...results]);
        }
      } catch (error) {
        console.error('🧪 Test 2 failed:', error);
        results[results.length - 1] = {
          test: 'Update Scoring Rule',
          status: 'error',
          message: 'Failed to update scoring rule',
          details: error instanceof Error ? error.message : 'Unknown error'
        };
        setTestResults([...results]);
      }
    } catch (error) {
      console.error('🧪 Test 1 failed:', error);
      results[results.length - 1] = {
        test: 'Create Scoring Rule',
        status: 'error',
        message: 'Failed to create scoring rule',
        details: error instanceof Error ? error.message : 'Unknown error'
      };
      setTestResults([...results]);
    }

    setIsRunning(false);
    
    const successCount = results.filter(r => r.status === 'success').length;
    const errorCount = results.filter(r => r.status === 'error').length;
    
    toast({
      title: 'Tests Complete',
      description: `${successCount} passed, ${errorCount} failed`,
      variant: errorCount > 0 ? 'destructive' : 'default'
    });
  };

  const handleRuleEditorSave = async (rule: Partial<ScoringRule>) => {
    console.log('🧪 RuleEditor save called with:', rule);
    
    if (onCreateScoringRule) {
      try {
        await onCreateScoringRule(mockQuestionId, rule);
        setShowRuleEditor(false);
        toast({
          title: 'Success',
          description: 'Rule created via RuleEditor test'
        });
      } catch (error) {
        console.error('🧪 RuleEditor test failed:', error);
        toast({
          title: 'Error',
          description: error instanceof Error ? error.message : 'Failed to create rule',
          variant: 'destructive'
        });
      }
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Passed</Badge>;
      case 'error':
        return <Badge variant="destructive">Failed</Badge>;
      case 'pending':
        return <Badge variant="secondary">Running...</Badge>;
    }
  };

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Play className="h-5 w-5" />
            Scoring Rule API Test Suite
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Button 
              onClick={runTests} 
              disabled={isRunning}
              variant="outline"
            >
              <Play className="h-4 w-4 mr-2" />
              {isRunning ? 'Running Tests...' : 'Run API Tests'}
            </Button>
            <Button 
              onClick={() => setShowRuleEditor(true)}
              variant="outline"
            >
              Test RuleEditor
            </Button>
            <Button 
              onClick={() => setTestResults([])}
              variant="ghost"
              size="sm"
            >
              Clear Results
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="font-medium">Create Function:</span>
              <Badge variant={onCreateScoringRule ? "default" : "destructive"} className="ml-2">
                {onCreateScoringRule ? 'Available' : 'Missing'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Update Function:</span>
              <Badge variant={onUpdateScoringRule ? "default" : "destructive"} className="ml-2">
                {onUpdateScoringRule ? 'Available' : 'Missing'}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Delete Function:</span>
              <Badge variant={onDeleteScoringRule ? "default" : "destructive"} className="ml-2">
                {onDeleteScoringRule ? 'Available' : 'Missing'}
              </Badge>
            </div>
          </div>

          {testResults.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">Test Results:</h4>
              {testResults.map((result, index) => (
                <Alert key={index} variant={result.status === 'error' ? 'destructive' : 'default'}>
                  <div className="flex items-start gap-2">
                    {getStatusIcon(result.status)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{result.test}</span>
                        {getStatusBadge(result.status)}
                      </div>
                      <AlertDescription className="mt-1">
                        <div>{result.message}</div>
                        {result.details && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {result.details}
                          </div>
                        )}
                      </AlertDescription>
                    </div>
                  </div>
                </Alert>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* RuleEditor Test Modal */}
      {showRuleEditor && (
        <RuleEditor
          isOpen={showRuleEditor}
          onClose={() => setShowRuleEditor(false)}
          onSave={handleRuleEditorSave}
          questionId={mockQuestionId}
          questionType={QuestionType.SHORT_TEXT}
          questionLabel={mockQuestionLabel}
          isRepeatable={false}
          allowBonusRules={false}
        />
      )}
    </>
  );
} 