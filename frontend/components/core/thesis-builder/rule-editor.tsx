"use client"

import React, { useState, useEffect } from 'react';
import { X, Save, AlertCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  ScoringRule,
  RuleType,
  ConditionOperator,
  AggregationType,
  FilterCondition,
  getOperatorDisplay,
  getAggregationDisplay
} from '@/lib/types/thesis';
import { QuestionType } from '@/lib/types/form';
import { RuleSummary } from './rule-summary';

interface RuleEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (rule: Partial<ScoringRule>) => void;
  rule?: Partial<ScoringRule>;
  questionId?: string;
  questionType?: QuestionType;
  questionLabel?: string;
  isRepeatable?: boolean;
  sectionId?: string;
  allowBonusRules?: boolean; // New prop to control bonus rule creation
}

// Get valid operators for a question type
function getValidOperators(questionType: QuestionType): ConditionOperator[] {
  switch (questionType) {
    case QuestionType.SHORT_TEXT:
    case QuestionType.LONG_TEXT:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.CONTAINS,
        ConditionOperator.NOT_CONTAINS,
        ConditionOperator.STARTS_WITH,
        ConditionOperator.ENDS_WITH
      ];
    case QuestionType.NUMBER:
    case QuestionType.RANGE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    case QuestionType.BOOLEAN:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
    case QuestionType.SINGLE_SELECT:
    case QuestionType.MULTI_SELECT:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.IN,
        ConditionOperator.NOT_IN,
        ConditionOperator.CONTAINS,
        ConditionOperator.NOT_CONTAINS
      ];
    case QuestionType.DATE:
      return [
        ConditionOperator.EQUALS,
        ConditionOperator.NOT_EQUALS,
        ConditionOperator.GREATER_THAN,
        ConditionOperator.LESS_THAN,
        ConditionOperator.GREATER_THAN_EQUALS,
        ConditionOperator.LESS_THAN_EQUALS,
        ConditionOperator.BETWEEN,
        ConditionOperator.NOT_BETWEEN
      ];
    default:
      return [ConditionOperator.EQUALS, ConditionOperator.NOT_EQUALS];
  }
}

export function RuleEditor({
  isOpen,
  onClose,
  onSave,
  rule,
  questionId,
  questionType = QuestionType.SHORT_TEXT,
  questionLabel,
  isRepeatable = false,
  sectionId,
  allowBonusRules = true // Default to true for backward compatibility
}: RuleEditorProps) {
  const [formData, setFormData] = useState<Partial<ScoringRule>>({
    rule_type: RuleType.SCORING,
    weight: 1.0,
    is_deleted: false,
    question_id: questionId || '', // Ensure question_id is set
    // REQUIRED: Always include condition field (backend validation requires this)
    condition: {
      question_id: questionId || '',
      operator: ConditionOperator.EQUALS,
      value: ''
    } as FilterCondition,
    ...rule
  });

  const [errors, setErrors] = useState<string[]>([]);
  const [warnings, setWarnings] = useState<string[]>([]);

  // Reset form when rule changes
  useEffect(() => {
    if (rule) {
      setFormData({
        ...rule,
        // Ensure question_id is set for the rule
        question_id: questionId || rule.question_id,
        // Ensure condition has the correct question_id - handle both FilterCondition and CompoundFilter
        condition: rule.condition ? (() => {
          // Check if it's a FilterCondition (has operator and value)
          if ('operator' in rule.condition && 'value' in rule.condition) {
            return {
              ...rule.condition,
              question_id: questionId || rule.question_id || (rule.condition as FilterCondition).question_id
            } as FilterCondition;
          } else {
            // It's a CompoundFilter, return as-is
            return rule.condition;
          }
        })() : {
          question_id: questionId || rule.question_id || '',
          operator: ConditionOperator.EQUALS,
          value: ''
        } as FilterCondition
      });
    } else {
      setFormData({
        rule_type: RuleType.SCORING,
        weight: 1.0,
        question_id: questionId || '',
        is_deleted: false,
        condition: {
          question_id: questionId || '',
          operator: ConditionOperator.EQUALS,
          value: ''
        } as FilterCondition
      });
    }
  }, [rule, questionId]);

  // Validate form
  useEffect(() => {
    const newErrors: string[] = [];
    const newWarnings: string[] = [];

    // CRITICAL: Validate condition field (required by backend for ALL rules)
    if (!formData.condition) {
      newErrors.push('Condition is required for all rules');
    } else if ('question_id' in formData.condition) {
      // Simple condition validation
      if (!formData.condition.question_id) {
        newErrors.push('Question ID is required in condition');
      }
      if (!formData.condition.operator) {
        newErrors.push('Operator is required in condition');
      }
      if (formData.condition.value === undefined || formData.condition.value === '') {
        newErrors.push('Condition value is required');
      }
    }

    if (formData.rule_type === RuleType.SCORING) {
      if (!formData.question_id) {
        newErrors.push('Question ID is required for scoring rules');
      }
      if (!formData.weight || formData.weight <= 0) {
        newErrors.push('Weight must be positive');
      }
    }

    if (formData.rule_type === RuleType.BONUS) {
      if (!formData.bonus_points || formData.bonus_points <= 0) {
        newErrors.push('Bonus points must be positive');
      }
    }

    if (isRepeatable && formData.aggregation) {
      if (formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE) {
        if (!formData.value_field) {
          newErrors.push('Value field is required for this aggregation type');
        }
      }
      if (!formData.value_field) {
        newWarnings.push('Value field is recommended for aggregation');
      }
    }

    setErrors(newErrors);
    setWarnings(newWarnings);
  }, [formData, isRepeatable]);

  const handleSave = () => {
    if (errors.length === 0) {
      // Ensure we have a clean payload that matches backend expectations
      const cleanedData: Partial<ScoringRule> = {
        rule_type: formData.rule_type || RuleType.SCORING,
        question_id: formData.question_id || questionId,
        weight: formData.weight || 1.0,
        condition: formData.condition,
        bonus_points: formData.bonus_points,
        aggregation: formData.aggregation,
        value_field: formData.value_field,
        notes: formData.notes,
        is_deleted: false
      };

      // Remove undefined values to avoid backend validation issues
      Object.keys(cleanedData).forEach(key => {
        if (cleanedData[key as keyof typeof cleanedData] === undefined) {
          delete cleanedData[key as keyof typeof cleanedData];
        }
      });

      console.log('💾 Saving rule with cleaned data:', cleanedData);
      onSave(cleanedData);
    }
  };

  const validOperators = getValidOperators(questionType);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {rule ? 'Edit Scoring Rule' : 'Create Scoring Rule'}
          </DialogTitle>
          <DialogDescription>
            Configure how this question will be scored in your thesis.
            {questionLabel && (
              <div className="mt-2 p-2 bg-muted rounded text-sm">
                <strong>Question:</strong> {questionLabel}
              </div>
            )}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Rule Type */}
          <div className="space-y-2">
            <Label>Rule Type</Label>
            <Select
              value={formData.rule_type}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                rule_type: value as RuleType
              }))}
              disabled={!allowBonusRules && formData.rule_type === RuleType.SCORING}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={RuleType.SCORING}>Scoring Rule</SelectItem>
                {allowBonusRules && (
                  <SelectItem value={RuleType.BONUS}>Bonus Rule</SelectItem>
                )}
              </SelectContent>
            </Select>
            {!allowBonusRules && (
              <p className="text-xs text-muted-foreground">
                Bonus rules can only be created in the Bonus tab
              </p>
            )}
          </div>

          {/* Weight (for scoring rules) */}
          {formData.rule_type === RuleType.SCORING && (
            <div className="space-y-2">
              <Label>Weight</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.weight || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  weight: e.target.value ? Number(e.target.value) : 1
                }))}
                placeholder="1.0"
              />
            </div>
          )}

          {/* Bonus Points (for bonus rules) */}
          {formData.rule_type === RuleType.BONUS && (
            <div className="space-y-2">
              <Label>Bonus Points</Label>
              <Input
                type="number"
                min="0"
                step="0.1"
                value={formData.bonus_points || ''}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  bonus_points: e.target.value ? Number(e.target.value) : 0
                }))}
                placeholder="2.0"
              />
            </div>
          )}

          {/* Condition Operator */}
          <div className="space-y-2">
            <Label>Operator</Label>
            <Select
              value={formData.condition && 'operator' in formData.condition ? formData.condition.operator : ''}
              onValueChange={(value) => setFormData(prev => ({
                ...prev,
                condition: {
                  question_id: questionId || prev.question_id || '',
                  operator: value as ConditionOperator,
                  value: (prev.condition as FilterCondition)?.value || ''
                }
              }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                {validOperators.map(op => (
                  <SelectItem key={op} value={op}>
                    {getOperatorDisplay(op)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Condition Value */}
          <div className="space-y-2">
            <Label>Expected Value</Label>
            <Input
              value={formData.condition && 'value' in formData.condition ? formData.condition.value : ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                condition: {
                  question_id: questionId || prev.question_id || '',
                  operator: (prev.condition as FilterCondition)?.operator || ConditionOperator.EQUALS,
                  value: e.target.value
                }
              }))}
              placeholder="Enter expected value"
            />
          </div>

          {/* Aggregation (for repeatable sections) */}
          {isRepeatable && (
            <>
              <div className="space-y-2">
                <Label>Aggregation Type</Label>
                <Select
                  value={formData.aggregation || ''}
                  onValueChange={(value) => setFormData(prev => ({
                    ...prev,
                    aggregation: value as AggregationType
                  }))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select aggregation" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.values(AggregationType).map(type => (
                      <SelectItem key={type} value={type}>
                        {getAggregationDisplay(type)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>
                  Value Field 
                  {formData.aggregation && formData.aggregation !== AggregationType.COUNT && formData.aggregation !== AggregationType.NONE 
                    ? ' (required)' 
                    : ' (optional)'
                  }
                </Label>
                <Input
                  value={formData.value_field || ''}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    value_field: e.target.value || undefined
                  }))}
                  placeholder="Field name for aggregation"
                />
              </div>
            </>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label>Notes (optional)</Label>
            <Textarea
              value={formData.notes || ''}
              onChange={(e) => setFormData(prev => ({
                ...prev,
                notes: e.target.value || undefined
              }))}
              placeholder="Additional notes about this rule"
              className="min-h-[60px]"
            />
          </div>

          {/* Rule Summary */}
          <div className="space-y-2">
            <Label>Rule Summary</Label>
            <div className="p-3 bg-muted rounded-md">
              <RuleSummary rule={formData} questionLabel={questionLabel} />
            </div>
          </div>

          {/* Validation Messages */}
          {errors.length > 0 && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}

          {warnings.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                <ul className="list-disc list-inside space-y-1">
                  {warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave} disabled={errors.length > 0}>
            <Save className="h-4 w-4 mr-2" />
            Save Rule
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
