"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { 
  Target, 
  Users, 
  TrendingUp, 
  CheckCircle,
  ExternalLink,
  ChevronRight
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface ScoreTabProps {
  deal: DealDetailData
}

const getScoreColor = (score: number) => {
  if (score >= 80) return 'text-green-600'
  if (score >= 60) return 'text-yellow-600'
  return 'text-red-600'
}

const getScoreBackground = (score: number) => {
  if (score >= 80) return 'bg-green-50 border-green-200'
  if (score >= 60) return 'bg-yellow-50 border-yellow-200'
  return 'bg-red-50 border-red-200'
}

const signals = [
  {
    id: 'team_strength',
    title: 'Team Strength',
    icon: Users,
    description: 'Founder experience, team composition, and track record'
  },
  {
    id: 'market_signals',
    title: 'Market Signals',
    icon: TrendingUp,
    description: 'Market size, growth trends, and competitive landscape'
  },
  {
    id: 'thesis_match',
    title: 'Thesis Match',
    icon: Target,
    description: 'Alignment with investment criteria and strategic focus'
  }
]

export function ScoreTab({ deal }: ScoreTabProps) {
  const [selectedSignal, setSelectedSignal] = useState<string | null>(null)
  const scoreBreakdown = deal.score_breakdown

  if (!scoreBreakdown) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="target" />
        <EmptyPlaceholder.Title>No scoring data available</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          This deal hasn't been scored yet. Scoring will appear here once the analysis is complete.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const handleViewFullAnalysis = () => {
    // TODO: Navigate to full analysis page
    console.log('View full analysis for deal:', deal.id)
  }

  return (
    <div className="space-y-8">
      {/* Overall Score */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <Card className={cn("border-2", getScoreBackground(scoreBreakdown.overall_score))}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold mb-2">Overall Score</h3>
                <p className="text-sm text-muted-foreground">
                  Last updated {new Date(scoreBreakdown.last_updated).toLocaleDateString()}
                </p>
              </div>
              <div className="text-right">
                <div className={cn("text-4xl font-bold", getScoreColor(scoreBreakdown.overall_score))}>
                  {scoreBreakdown.overall_score}
                </div>
                <div className="text-sm text-muted-foreground">out of 100</div>
              </div>
            </div>
            
            <div className="mt-4">
              <Progress 
                value={scoreBreakdown.overall_score} 
                className="h-2"
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Signal Breakdown */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Signal Breakdown</h3>
          <Button 
            variant="outline" 
            onClick={handleViewFullAnalysis}
            className="gap-2"
          >
            View Full Analysis
            <ExternalLink className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid gap-4">
          {signals.map((signal, index) => {
            const signalData = scoreBreakdown.signals[signal.id as keyof typeof scoreBreakdown.signals]
            const Icon = signal.icon
            const isSelected = selectedSignal === signal.id
            
            return (
              <motion.div
                key={signal.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Card 
                  className={cn(
                    "cursor-pointer transition-all hover:shadow-md",
                    isSelected && "ring-2 ring-primary"
                  )}
                  onClick={() => setSelectedSignal(isSelected ? null : signal.id)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-10 h-10 rounded-lg bg-muted">
                          <Icon className="h-5 w-5" />
                        </div>
                        <div>
                          <CardTitle className="text-base">{signal.title}</CardTitle>
                          <p className="text-sm text-muted-foreground">
                            {signal.description}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <div className="text-right">
                          <div className={cn("text-2xl font-bold", getScoreColor(signalData.score))}>
                            {signalData.score}
                          </div>
                          <div className="text-xs text-muted-foreground">score</div>
                        </div>
                        <ChevronRight className={cn(
                          "h-4 w-4 transition-transform",
                          isSelected && "rotate-90"
                        )} />
                      </div>
                    </div>
                  </CardHeader>
                  
                  {isSelected && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <CardContent className="pt-0">
                        <div className="border-t pt-4">
                          <div className="space-y-3">
                            <div>
                              <h4 className="font-medium text-sm mb-2">Analysis</h4>
                              <p className="text-sm text-muted-foreground">
                                {signalData.explanation}
                              </p>
                            </div>
                            
                            <div className="flex items-center gap-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <span className="text-sm">Analysis complete</span>
                            </div>
                            
                            <Progress 
                              value={signalData.score} 
                              className="h-1.5"
                            />
                          </div>
                        </div>
                      </CardContent>
                    </motion.div>
                  )}
                </Card>
              </motion.div>
            )
          })}
        </div>
      </div>

      {/* Quick Insights */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Key Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-green-500 mt-2" />
                <div>
                  <p className="text-sm font-medium">Strong founding team</p>
                  <p className="text-xs text-muted-foreground">
                    Experienced founders with relevant domain expertise
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-yellow-500 mt-2" />
                <div>
                  <p className="text-sm font-medium">Growing market opportunity</p>
                  <p className="text-xs text-muted-foreground">
                    Market showing positive growth trends and investor interest
                  </p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 rounded-full bg-blue-500 mt-2" />
                <div>
                  <p className="text-sm font-medium">Good thesis alignment</p>
                  <p className="text-xs text-muted-foreground">
                    Aligns well with investment focus areas and criteria
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
