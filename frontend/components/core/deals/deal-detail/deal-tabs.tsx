"use client"

import { motion } from "framer-motion"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { DealDetailData } from "@/lib/types/deal-detail"
import { TimelineTab } from "./timeline-tab"
import { ScoreTab } from "./score-tab"
import { FoundersTab } from "./founders-tab"
import { SignalsTab } from "./signals-tab"
import { DocumentsTab } from "./documents-tab"
import { BenchmarksTab } from "./benchmarks-tab"
import { 
  Clock, 
  Target, 
  Users, 
  Radio, 
  FileText, 
  BarChart3 
} from "lucide-react"

interface DealTabsProps {
  deal: DealDetailData
  activeTab: string
  onTabChange: (tab: string) => void
}

const tabs = [
  {
    id: 'timeline',
    label: 'Timeline',
    icon: Clock,
    component: TimelineTab
  },
  {
    id: 'score',
    label: 'Score',
    icon: Target,
    component: ScoreTab
  },
  {
    id: 'founders',
    label: 'Founders',
    icon: Users,
    component: FoundersTab
  },
  {
    id: 'signals',
    label: 'External Signals',
    icon: Radio,
    component: SignalsTab
  },
  {
    id: 'documents',
    label: 'Documents',
    icon: FileText,
    component: DocumentsTab
  },
  {
    id: 'benchmarks',
    label: 'Benchmarks',
    icon: BarChart3,
    component: BenchmarksTab,
    disabled: true
  }
]

export function DealTabs({ deal, activeTab, onTabChange }: DealTabsProps) {
  const getTabCount = (tabId: string) => {
    switch (tabId) {
      case 'timeline':
        return deal.timeline?.length || 0
      case 'founders':
        return deal.founders?.length || 0
      case 'signals':
        return deal.external_signals?.length || 0
      case 'documents':
        return deal.documents?.length || 0
      default:
        return null
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <Tabs value={activeTab} onValueChange={onTabChange} className="w-full">
        {/* Tab Navigation */}
        <div className="sticky top-0 z-10 bg-background border-b">
          <TabsList className="grid w-full grid-cols-6 h-auto p-1 bg-muted/50">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const count = getTabCount(tab.id)
              
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  disabled={tab.disabled}
                  className="flex items-center gap-2 py-3 px-4 data-[state=active]:bg-background data-[state=active]:shadow-sm relative"
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{tab.label}</span>
                  
                  {count !== null && count > 0 && (
                    <Badge 
                      variant="secondary" 
                      className="ml-1 h-5 min-w-[20px] text-xs px-1.5"
                    >
                      {count}
                    </Badge>
                  )}
                  
                  {tab.disabled && (
                    <Badge 
                      variant="outline" 
                      className="ml-1 h-5 text-xs px-1.5 text-muted-foreground"
                    >
                      Soon
                    </Badge>
                  )}
                </TabsTrigger>
              )
            })}
          </TabsList>
        </div>

        {/* Tab Content */}
        <div className="mt-6">
          {tabs.map((tab) => {
            const Component = tab.component
            
            return (
              <TabsContent 
                key={tab.id} 
                value={tab.id}
                className="mt-0 focus-visible:outline-none"
              >
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.2 }}
                >
                  <Component deal={deal} />
                </motion.div>
              </TabsContent>
            )
          })}
        </div>
      </Tabs>
    </motion.div>
  )
}
