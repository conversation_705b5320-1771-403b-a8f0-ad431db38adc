"use client"

import { useState, useRef } from "react"
import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Upload, 
  FileText, 
  Download, 
  Eye, 
  Trash2,
  File,
  Image,
  FileSpreadsheet,
  Monitor,
  Calendar,
  User
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, DealDocument } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"
import { useToast } from "@/components/ui/use-toast"
import { DealDetailAPI } from "@/lib/api/deal-detail-api"

interface DocumentsTabProps {
  deal: DealDetailData
}

const getFileIcon = (type: DealDocument['type']) => {
  switch (type) {
    case 'pdf':
      return FileText
    case 'doc':
      return FileText
    case 'xls':
      return FileSpreadsheet
    case 'ppt':
      return Monitor
    case 'image':
      return Image
    default:
      return File
  }
}

const getFileColor = (type: DealDocument['type']) => {
  switch (type) {
    case 'pdf':
      return 'bg-red-100 text-red-600 border-red-200'
    case 'doc':
      return 'bg-blue-100 text-blue-600 border-blue-200'
    case 'xls':
      return 'bg-green-100 text-green-600 border-green-200'
    case 'ppt':
      return 'bg-orange-100 text-orange-600 border-orange-200'
    case 'image':
      return 'bg-purple-100 text-purple-600 border-purple-200'
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200'
  }
}

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

export function DocumentsTab({ deal }: DocumentsTabProps) {
  const [documents, setDocuments] = useState(deal.documents || [])
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const handleUploadClick = () => {
    fileInputRef.current?.click()
  }

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files || files.length === 0) return

    const file = files[0]
    
    // Basic validation
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast({
        title: "File too large",
        description: "Please select a file smaller than 10MB.",
        variant: "destructive"
      })
      return
    }

    try {
      setUploading(true)
      const uploadedDoc = await DealDetailAPI.uploadDocument(deal.id, file)
      setDocuments(prev => [uploadedDoc, ...prev])
      
      toast({
        title: "File uploaded",
        description: `${file.name} has been uploaded successfully.`
      })
    } catch (error) {
      console.error('Upload error:', error)
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file. Please try again.",
        variant: "destructive"
      })
    } finally {
      setUploading(false)
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handlePreview = (document: DealDocument) => {
    if (document.preview_url) {
      window.open(document.preview_url, '_blank', 'noopener,noreferrer')
    } else {
      // Fallback to download
      handleDownload(document)
    }
  }

  const handleDownload = (document: DealDocument) => {
    window.open(document.url, '_blank', 'noopener,noreferrer')
  }

  const handleDelete = async (document: DealDocument) => {
    if (!document.can_delete) {
      toast({
        title: "Cannot delete",
        description: "You don't have permission to delete this document.",
        variant: "destructive"
      })
      return
    }

    try {
      await DealDetailAPI.deleteDocument(deal.id, document.id)
      setDocuments(prev => prev.filter(doc => doc.id !== document.id))
      
      toast({
        title: "Document deleted",
        description: `${document.name} has been deleted.`
      })
    } catch (error) {
      console.error('Delete error:', error)
      toast({
        title: "Delete failed",
        description: "There was an error deleting the document. Please try again.",
        variant: "destructive"
      })
    }
  }

  if (documents.length === 0) {
    return (
      <div className="space-y-6">
        {/* Upload Area */}
        <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                  <Upload className="h-8 w-8 text-muted-foreground" />
                </div>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Upload documents</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Drag and drop files here, or click to browse
                </p>
                <Button onClick={handleUploadClick} disabled={uploading}>
                  {uploading ? 'Uploading...' : 'Choose Files'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <input
          ref={fileInputRef}
          type="file"
          onChange={handleFileSelect}
          className="hidden"
          accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
        />

        <EmptyPlaceholder>
          <EmptyPlaceholder.Icon name="page" />
          <EmptyPlaceholder.Title>No documents yet</EmptyPlaceholder.Title>
          <EmptyPlaceholder.Description>
            Upload documents to share with your team and track deal progress.
          </EmptyPlaceholder.Description>
        </EmptyPlaceholder>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card className="border-dashed border-2 hover:border-primary/50 transition-colors">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Upload new document</h3>
              <p className="text-sm text-muted-foreground">
                PDF, Word, Excel, PowerPoint, and images supported
              </p>
            </div>
            <Button onClick={handleUploadClick} disabled={uploading} className="gap-2">
              <Upload className="h-4 w-4" />
              {uploading ? 'Uploading...' : 'Upload'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <input
        ref={fileInputRef}
        type="file"
        onChange={handleFileSelect}
        className="hidden"
        accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.png,.jpg,.jpeg,.gif"
      />

      {/* Documents List */}
      <div className="space-y-3">
        {documents
          .sort((a, b) => new Date(b.uploaded_at).getTime() - new Date(a.uploaded_at).getTime())
          .map((document, index) => {
            const Icon = getFileIcon(document.type)
            
            return (
              <motion.div
                key={document.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Card className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-4">
                      {/* File Icon */}
                      <div className={cn(
                        "flex items-center justify-center w-12 h-12 rounded-lg border",
                        getFileColor(document.type)
                      )}>
                        <Icon className="h-6 w-6" />
                      </div>

                      {/* File Info */}
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm truncate">
                          {document.name}
                        </h4>
                        <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                          <span>{formatFileSize(document.size)}</span>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatDate(document.uploaded_at)}
                          </div>
                          {document.uploaded_by_name && (
                            <div className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {document.uploaded_by_name}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* File Type Badge */}
                      <Badge variant="outline" className="uppercase text-xs">
                        {document.type}
                      </Badge>

                      {/* Actions */}
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(document)}
                          className="gap-1"
                        >
                          <Eye className="h-4 w-4" />
                          Preview
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(document)}
                          className="gap-1"
                        >
                          <Download className="h-4 w-4" />
                          Download
                        </Button>
                        
                        {document.can_delete && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(document)}
                            className="gap-1 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
      </div>

      {/* Summary */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">
              {documents.length} document{documents.length !== 1 ? 's' : ''} total
            </span>
            <span className="text-muted-foreground">
              {formatFileSize(documents.reduce((acc, doc) => acc + doc.size, 0))} total size
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
