"use client"

import { useState } from "react"
import Link from "next/link"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ChevronLeft, 
  FileText, 
  Share2, 
  MessageSquare,
  MoreHorizontal,
  Star,
  Flag
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData } from "@/lib/types/deal-detail"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface DealHeaderProps {
  deal: DealDetailData
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'new':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'active':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'triage':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    case 'completed':
      return 'bg-gray-100 text-gray-800 border-gray-200'
    case 'flagged':
      return 'bg-red-100 text-red-800 border-red-200'
    case 'hard_pass':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

const getStageColor = (stage: string) => {
  switch (stage?.toLowerCase()) {
    case 'pre-seed':
      return 'bg-purple-100 text-purple-800 border-purple-200'
    case 'seed':
      return 'bg-blue-100 text-blue-800 border-blue-200'
    case 'series a':
      return 'bg-green-100 text-green-800 border-green-200'
    case 'series b':
      return 'bg-orange-100 text-orange-800 border-orange-200'
    case 'series c':
      return 'bg-red-100 text-red-800 border-red-200'
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200'
  }
}

export function DealHeader({ deal }: DealHeaderProps) {
  const [isStarred, setIsStarred] = useState(false)

  const handleStarToggle = () => {
    setIsStarred(!isStarred)
    // TODO: Implement star/bookmark functionality
  }

  const handleShare = () => {
    // TODO: Implement share functionality
    console.log('Share deal:', deal.id)
  }

  const handleCreateMemo = () => {
    // TODO: Implement deal memo creation
    console.log('Create memo for deal:', deal.id)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-6"
    >
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-muted-foreground">
        <Link 
          href="/deals" 
          className="flex items-center hover:text-foreground transition-colors"
        >
          <ChevronLeft className="h-4 w-4 mr-1" />
          Back to Deals
        </Link>
      </div>

      {/* Main Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        {/* Left Side - Company Info */}
        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold tracking-tight">
              {deal.company_name || 'Unnamed Company'}
            </h1>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleStarToggle}
              className={cn(
                "p-1 h-8 w-8",
                isStarred ? "text-yellow-500" : "text-muted-foreground"
              )}
            >
              <Star className={cn("h-4 w-4", isStarred && "fill-current")} />
            </Button>
          </div>

          <div className="flex flex-wrap items-center gap-2">
            <Badge 
              variant="outline" 
              className={cn("font-medium", getStatusColor(deal.status))}
            >
              {deal.status.charAt(0).toUpperCase() + deal.status.slice(1).replace('_', ' ')}
            </Badge>
            
            {deal.stage && (
              <Badge 
                variant="outline" 
                className={cn("font-medium", getStageColor(deal.stage))}
              >
                {deal.stage}
              </Badge>
            )}
            
            {deal.sector && (
              <Badge variant="secondary">
                {Array.isArray(deal.sector) ? deal.sector[0] : deal.sector}
              </Badge>
            )}

            {deal.score_breakdown && (
              <Badge variant="outline" className="font-mono">
                Score: {deal.score_breakdown.overall_score}
              </Badge>
            )}
          </div>

          {/* Quick Stats */}
          <div className="flex items-center gap-6 text-sm text-muted-foreground">
            <span>
              {deal.founders?.length || 0} founder{deal.founders?.length !== 1 ? 's' : ''}
            </span>
            <span>
              {deal.documents?.length || 0} document{deal.documents?.length !== 1 ? 's' : ''}
            </span>
            <span>
              {deal.external_signals?.length || 0} signal{deal.external_signals?.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>

        {/* Right Side - Actions */}
        <div className="flex items-center gap-3">
          <Button 
            variant="outline" 
            onClick={handleCreateMemo}
            className="gap-2"
          >
            <FileText className="h-4 w-4" />
            Create Memo
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleShare}
            className="gap-2"
          >
            <Share2 className="h-4 w-4" />
            Share
          </Button>

          <Button className="gap-2">
            <MessageSquare className="h-4 w-4" />
            Open Inbox
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>
                <Flag className="h-4 w-4 mr-2" />
                Flag Deal
              </DropdownMenuItem>
              <DropdownMenuItem>
                Export Data
              </DropdownMenuItem>
              <DropdownMenuItem className="text-red-600">
                Archive Deal
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </motion.div>
  )
}
