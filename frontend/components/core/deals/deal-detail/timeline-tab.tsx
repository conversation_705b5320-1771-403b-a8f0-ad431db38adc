"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { 
  Clock, 
  FileText, 
  Target, 
  User, 
  Settings,
  CheckCircle
} from "lucide-react"
import { cn } from "@/lib/utils"
import { DealDetailData, TimelineEvent } from "@/lib/types/deal-detail"
import { EmptyPlaceholder } from "@/components/empty-placeholder"

interface TimelineTabProps {
  deal: DealDetailData
}

const getEventIcon = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return Settings
    case 'user':
      return User
    case 'score':
      return Target
    case 'document':
      return FileText
    case 'status':
      return CheckCircle
    default:
      return Clock
  }
}

const getEventColor = (type: TimelineEvent['type']) => {
  switch (type) {
    case 'system':
      return 'bg-gray-100 text-gray-600 border-gray-200'
    case 'user':
      return 'bg-blue-100 text-blue-600 border-blue-200'
    case 'score':
      return 'bg-green-100 text-green-600 border-green-200'
    case 'document':
      return 'bg-purple-100 text-purple-600 border-purple-200'
    case 'status':
      return 'bg-orange-100 text-orange-600 border-orange-200'
    default:
      return 'bg-gray-100 text-gray-600 border-gray-200'
  }
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 48) {
    return 'Yesterday'
  } else {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== now.getFullYear() ? 'numeric' : undefined
    })
  }
}

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit',
    hour12: true
  })
}

const groupEventsByDate = (events: TimelineEvent[]) => {
  const groups: { [key: string]: TimelineEvent[] } = {}
  
  events.forEach(event => {
    const date = new Date(event.date).toDateString()
    if (!groups[date]) {
      groups[date] = []
    }
    groups[date].push(event)
  })
  
  return Object.entries(groups).sort(([a], [b]) => 
    new Date(b).getTime() - new Date(a).getTime()
  )
}

export function TimelineTab({ deal }: TimelineTabProps) {
  const events = deal.timeline || []
  
  if (events.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="clock" />
        <EmptyPlaceholder.Title>No timeline events yet</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          Timeline events will appear here as actions are taken on this deal.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    )
  }

  const groupedEvents = groupEventsByDate(events)

  return (
    <div className="space-y-8">
      {groupedEvents.map(([dateString, dayEvents], groupIndex) => (
        <motion.div
          key={dateString}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: groupIndex * 0.1 }}
          className="space-y-4"
        >
          {/* Date Header */}
          <div className="flex items-center gap-4">
            <h3 className="text-sm font-medium text-muted-foreground">
              {formatDate(dayEvents[0].date)}
            </h3>
            <div className="flex-1 h-px bg-border" />
          </div>

          {/* Events for this date */}
          <div className="space-y-3">
            {dayEvents
              .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
              .map((event, eventIndex) => {
                const Icon = getEventIcon(event.type)
                
                return (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.2, delay: eventIndex * 0.05 }}
                  >
                    <Card className="border-l-4 border-l-muted hover:shadow-sm transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                          {/* Event Icon */}
                          <div className={cn(
                            "flex items-center justify-center w-8 h-8 rounded-full border",
                            getEventColor(event.type)
                          )}>
                            <Icon className="h-4 w-4" />
                          </div>

                          {/* Event Content */}
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between gap-4">
                              <div className="flex-1">
                                <h4 className="font-medium text-sm">
                                  {event.event}
                                </h4>
                                {event.description && (
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {event.description}
                                  </p>
                                )}
                              </div>
                              
                              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                                <span>{formatTime(event.date)}</span>
                              </div>
                            </div>

                            {/* Event Metadata */}
                            <div className="flex items-center gap-2 mt-2">
                              {event.user_name && (
                                <div className="flex items-center gap-1.5">
                                  <Avatar className="h-5 w-5">
                                    <AvatarFallback className="text-xs">
                                      {event.user_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                                    </AvatarFallback>
                                  </Avatar>
                                  <span className="text-xs text-muted-foreground">
                                    {event.user_name}
                                  </span>
                                </div>
                              )}
                              
                              <Badge 
                                variant="outline" 
                                className="text-xs h-5"
                              >
                                {event.type}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
          </div>
        </motion.div>
      ))}
    </div>
  )
}
