"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  Filter, 
  FileText, 
  Zap,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface DealsHeaderProps {
  onSearchChange?: (search: string) => void;
  onFilterChange?: (filter: string) => void;
  activeFilter?: string;
  totalDeals?: number;
}

const filterOptions = [
  { value: 'all', label: 'All', count: 10 },
  { value: 'new', label: 'New', count: 2 },
  { value: 'triage', label: 'Triage', count: 3 },
  { value: 'active', label: 'Active', count: 4 },
  { value: 'completed', label: 'Completed', count: 1 },
  { value: 'tracking', label: 'Tracking', count: 3 },
  { value: 'flagged', label: 'Flagged', count: 3 }
];

export function DealsHeader({ 
  onSearchChange, 
  onFilterChange, 
  activeFilter = 'all',
  totalDeals = 0 
}: DealsHeaderProps) {
  const [searchValue, setSearchValue] = useState('');

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onSearchChange?.(value);
  };

  const handleFilterClick = (filter: string) => {
    onFilterChange?.(filter);
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const filterVariants = {
    hidden: { opacity: 0, x: -10 },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    }
  };

  return (
    <motion.div
      variants={headerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Main Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Deal Management</h1>
          <p className="text-gray-600 mt-1">Track and manage your investment opportunities</p>
        </div>
        
        {/* Right Side Actions */}
        <div className="flex items-center gap-3">
          <Button variant="outline" size="sm" disabled className="gap-2">
            <Filter className="h-4 w-4" />
            Hard Pass Filters
          </Button>
          
          <Link href="/forms">
            <Button variant="outline" size="sm" className="gap-2">
              <FileText className="h-4 w-4" />
              Deal Forms
            </Button>
          </Link>
          
          <Button variant="outline" size="sm" disabled className="gap-2">
            <Zap className="h-4 w-4" />
            Auto-Match
          </Button>
        </div>
      </div>

      {/* Search and New Deal */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Search deals..."
            value={searchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10 bg-white border-gray-200"
          />
        </div>
        
        <Button size="sm" disabled className="gap-2 whitespace-nowrap">
          <Plus className="h-4 w-4" />
          New
        </Button>
      </div>

      {/* Filter Chips */}
      <motion.div 
        className="flex flex-wrap gap-2"
        variants={filterVariants}
      >
        {filterOptions.map((filter, index) => {
          const isActive = activeFilter === filter.value;
          
          return (
            <motion.div
              key={filter.value}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: index * 0.05, duration: 0.2 }}
            >
              <Button
                variant={isActive ? "default" : "outline"}
                size="sm"
                onClick={() => handleFilterClick(filter.value)}
                className={cn(
                  "gap-2 transition-all duration-200",
                  isActive 
                    ? "bg-gray-900 text-white hover:bg-gray-800" 
                    : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                )}
              >
                {filter.value === 'new' && <div className="w-2 h-2 bg-orange-400 rounded-full" />}
                {filter.value === 'triage' && <div className="w-2 h-2 bg-yellow-400 rounded-full" />}
                {filter.value === 'active' && <div className="w-2 h-2 bg-green-400 rounded-full" />}
                {filter.value === 'completed' && <div className="w-2 h-2 bg-gray-400 rounded-full" />}
                {filter.value === 'tracking' && <div className="w-2 h-2 bg-blue-400 rounded-full" />}
                {filter.value === 'flagged' && <div className="w-2 h-2 bg-red-400 rounded-full" />}
                
                <span className="font-medium">{filter.label}</span>
                
                <Badge 
                  variant="secondary" 
                  className={cn(
                    "text-xs",
                    isActive 
                      ? "bg-white/20 text-white border-white/20" 
                      : "bg-gray-100 text-gray-600 border-gray-200"
                  )}
                >
                  {filter.count}
                </Badge>
              </Button>
            </motion.div>
          );
        })}
      </motion.div>

      {/* Stats Summary */}
      <motion.div 
        className="flex flex-wrap gap-6 text-sm text-gray-600"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3, duration: 0.3 }}
      >
        <div className="flex items-center gap-2">
          <span className="font-medium">All</span>
          <Badge variant="outline" className="text-xs">
            {totalDeals}
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">Process</span>
          <Badge variant="outline" className="text-xs">
            3
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">Consider</span>
          <Badge variant="outline" className="text-xs">
            4
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">Pass</span>
          <Badge variant="outline" className="text-xs">
            0
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium">Track</span>
          <Badge variant="outline" className="text-xs">
            3
          </Badge>
        </div>
        <div className="flex items-center gap-2">
          <span className="font-medium text-red-600">Flagged</span>
          <Badge variant="outline" className="text-xs border-red-200 text-red-600">
            3
          </Badge>
        </div>
      </motion.div>
    </motion.div>
  );
}
