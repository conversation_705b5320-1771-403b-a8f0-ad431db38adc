"use client"

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Deal } from '@/lib/types/deal';
import { DealCard } from './deal-card';
import { DealModal } from './deal-modal';
import { Skeleton } from '@/components/ui/skeleton';
import { EmptyPlaceholder } from '@/components/empty-placeholder';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface DealsGridProps {
  deals: Deal[];
  loading: boolean;
  error: string | null;
}

export function DealsGrid({ deals, loading, error }: DealsGridProps) {
  const [selectedDeal, setSelectedDeal] = useState<Deal | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleDealClick = (deal: Deal) => {
    setSelectedDeal(deal);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setTimeout(() => setSelectedDeal(null), 300); // Wait for animation to complete
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <DealCardSkeleton key={index} />
        ))}
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive" className="max-w-md mx-auto">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  // Empty state
  if (!deals || deals.length === 0) {
    return (
      <EmptyPlaceholder>
        <EmptyPlaceholder.Icon name="post" />
        <EmptyPlaceholder.Title>No deals yet</EmptyPlaceholder.Title>
        <EmptyPlaceholder.Description>
          No deals have been created yet. Deals will appear here once they're added to your pipeline.
        </EmptyPlaceholder.Description>
      </EmptyPlaceholder>
    );
  }

  // Deals grid
  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6"
      >
        {deals.map((deal, index) => (
          <DealCard
            key={deal.id}
            deal={deal}
            index={index}
          />
        ))}
      </motion.div>

      {/* Coming Soon Modal */}
      <DealModal
        deal={selectedDeal}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
      />
    </>
  );
}

// Skeleton component for loading state
function DealCardSkeleton() {
  return (
    <div className="h-full">
      <div className="border border-gray-200 rounded-lg p-7 bg-white min-h-[240px]">
        {/* Header Section */}
        <div className="flex items-start justify-between mb-5">
          <div className="flex items-center gap-4 flex-1 min-w-0">
            <Skeleton className="h-14 w-14 rounded-full flex-shrink-0" />
            <div className="flex-1 min-w-0 space-y-3">
              <Skeleton className="h-6 w-3/4" />
              {/* Sector Tags Skeleton */}
              <div className="flex flex-wrap gap-2">
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-20 rounded-full" />
                <Skeleton className="h-5 w-12 rounded-full" />
              </div>
            </div>
          </div>
          <Skeleton className="h-7 w-20 rounded-full flex-shrink-0" />
        </div>

        {/* Description */}
        <div className="mb-5 space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>

        {/* Metrics Row */}
        <div className="flex items-center justify-between mb-5">
          <div className="flex items-center gap-3">
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
          <Skeleton className="h-4 w-20" />
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <Skeleton className="h-3 w-28" />
          <div className="flex items-center gap-1">
            <Skeleton className="h-1.5 w-1.5 rounded-full" />
            <Skeleton className="h-1.5 w-1.5 rounded-full" />
            <Skeleton className="h-1.5 w-1.5 rounded-full" />
          </div>
        </div>
      </div>
    </div>
  );
}
