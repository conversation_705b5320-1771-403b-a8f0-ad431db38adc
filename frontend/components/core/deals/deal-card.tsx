"use client"

import Link from 'next/link';
import { motion } from 'framer-motion';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { Deal } from '@/lib/types/deal';
import { getStageColor } from '@/lib/mock-data/deals';

interface DealCardProps {
  deal: Deal;
  index: number;
  onClick?: (deal: Deal) => void;
}

// Avatar color palette based on company name hash
const avatarColors = [
  'bg-blue-100 text-blue-700',
  'bg-emerald-100 text-emerald-700',
  'bg-violet-100 text-violet-700',
  'bg-amber-100 text-amber-700',
  'bg-rose-100 text-rose-700',
  'bg-indigo-100 text-indigo-700',
  'bg-cyan-100 text-cyan-700',
  'bg-orange-100 text-orange-700',
  'bg-teal-100 text-teal-700',
  'bg-pink-100 text-pink-700'
];

// Score color scheme
const getScoreColor = (score: number): string => {
  if (score >= 90) return 'bg-emerald-50 text-emerald-700 border-emerald-200';
  if (score >= 80) return 'bg-blue-50 text-blue-700 border-blue-200';
  if (score >= 70) return 'bg-amber-50 text-amber-700 border-amber-200';
  return 'bg-rose-50 text-rose-700 border-rose-200';
};

export function DealCard({ deal, index, onClick }: DealCardProps) {
  const cardVariants = {
    hidden: {
      opacity: 0,
      y: 20
    },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.3,
        ease: "easeOut",
        delay: index * 0.05
      }
    }
  };

  // Extract data with fallbacks
  const companyName = deal.company_name || 'Unnamed Company';
  const sectors = Array.isArray(deal.sector) 
    ? deal.sector 
    : deal.sector ? [deal.sector] : ['Technology'];
  const stage = deal.stage || 'Unknown';

  // Generate initials
  const initials = companyName
    .split(' ')
    .map(word => word.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');

  // Get avatar color based on company name hash
  const colorIndex = companyName.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % avatarColors.length;
  const avatarColor = avatarColors[colorIndex];

  // Mock additional data (in real app, this would come from API)
  const mockDescription = `Innovative ${sectors[0].toLowerCase()} solution transforming the industry with cutting-edge technology and advanced methodologies.`;
  const mockSource = ['Website'][Math.floor(Math.random() * 1)];
  const mockCountry = ['Thailand'][Math.floor(Math.random() * 1)];
  const mockScore = Math.floor(Math.random() * 40) + 60; // 60-100 range

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className="group"
    >
      <Link href={`/deals/${deal.id}`}>
        <Card className="relative overflow-hidden border border-gray-200 hover:border-gray-300 hover:shadow-lg transition-all duration-300 bg-white min-h-[240px] cursor-pointer">
        <CardContent className="p-7">
          {/* Header Section */}
          <div className="flex items-start justify-between mb-5">
            <div className="flex items-center gap-4 flex-1 min-w-0">
              <Avatar className="h-14 w-14 border-2 border-white shadow-sm flex-shrink-0">
                <AvatarFallback className={cn("text-base font-semibold", avatarColor)}>
                  {initials}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1 min-w-0">
                <h3 className="text-xl font-bold text-gray-900 leading-tight mb-3 group-hover:text-blue-600 transition-colors">
                  {companyName}
                </h3>
                
                {/* Sector Tags */}
                <div className="flex flex-wrap gap-2">
                  {sectors.slice(0, 3).map((sector, idx) => (
                    <Badge 
                      key={idx}
                      variant="outline" 
                      className="text-xs font-medium bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100 uppercase"
                    >
                      {sector.replace(/_/g, ' ')}
                    </Badge>
                  ))}
                  {sectors.length > 3 && (
                    <Badge 
                      variant="outline" 
                      className="text-xs font-medium bg-gray-50 text-gray-500 border-gray-200"
                    >
                      +{sectors.length - 3}
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            {/* Stage Badge */}
            <Badge
              variant="secondary"
              className={cn(
                "text-sm font-medium ml-4 flex-shrink-0 px-3 py-1 uppercase",
                getStageColor(stage)
              )}
            >
              {stage.replace(/_/g, ' ')}
            </Badge>
          </div>

          {/* Description */}
          <div className="mb-5">
            <p className="text-sm text-gray-600 leading-relaxed line-clamp-2">
              {mockDescription}
            </p>
          </div>

          {/* Metrics Row */}
          <div className="flex items-center justify-between mb-5">
            <div className="flex items-center gap-3">
              <Badge
                variant="outline"
                className={cn(
                  "text-xs font-semibold border px-2 py-1",
                  getScoreColor(mockScore)
                )}
              >
                Score: {mockScore}
              </Badge>
              <Badge variant="outline" className="text-xs text-gray-600 bg-gray-50 border-gray-200 px-2 py-1">
                {mockSource}
              </Badge>
            </div>
            <span className="text-sm text-gray-500 font-medium">
              {mockCountry}
            </span>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-100">
            <span className="text-xs text-gray-500">
              Click to view details
            </span>
            <div className="flex items-center gap-1">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full group-hover:bg-blue-600 transition-colors" />
              <div className="w-1.5 h-1.5 bg-blue-300 rounded-full group-hover:bg-blue-500 transition-colors" />
              <div className="w-1.5 h-1.5 bg-blue-200 rounded-full group-hover:bg-blue-400 transition-colors" />
            </div>
          </div>
        </CardContent>

        {/* Subtle hover overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/0 to-indigo-50/0 group-hover:from-blue-50/5 group-hover:to-indigo-50/5 transition-all duration-300 pointer-events-none" />
        </Card>
      </Link>
    </motion.div>
  );
}
