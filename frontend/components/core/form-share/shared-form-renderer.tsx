"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/icons';
import { ErrorBoundary } from '@/components/error-boundary';
import { SharedFormHeader } from './shared-form-header';
import { FormSection } from './form-section';
import { MagicLinkAuth } from './magic-link-auth';
import { SubmissionSuccess } from './submission-success';
import { useSharedForm } from '@/lib/hooks/use-shared-form';
import { useFormProgress } from '@/lib/hooks/use-form-progress';
import { validateAllAnswers } from '@/lib/utils/form-validation';
import { getVisibleQuestions } from '@/lib/utils/form-logic';

// Safe animation wrapper to prevent chunk loading errors
const SafeMotionDiv = ({ children, ...props }: any) => {
  try {
    return <motion.div {...props}>{children}</motion.div>;
  } catch (error) {
    console.warn('Animation error, falling back to static div:', error);
    return <div>{children}</div>;
  }
};

interface SharedFormRendererProps {
  token: string;
}



function SharedFormRendererInner({ token }: SharedFormRendererProps) {
  const [showMagicLink, setShowMagicLink] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [submissionId, setSubmissionId] = useState<string | null>(null);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side to prevent hydration mismatches
  useEffect(() => {
    setIsClient(true);
  }, []);

  const {
    formData,
    loading,
    error,
    submitForm,
    submitting
  } = useSharedForm(token);

  const {
    answers,
    updateAnswer,
    progress,
    saveProgress,
    autoSaveEnabled,
    removeRepeatAnswers,
    canSubmit
  } = useFormProgress(token, formData?.form);

  // Only validate after submit attempt
  useEffect(() => {
    if (formData?.form && hasAttemptedSubmit && isClient) {
      try {
        // Only validate visible and required questions
        const visibleQuestions = getVisibleQuestions(formData.form, answers);
        const validationErrors = validateAllAnswers(visibleQuestions, answers);
        setErrors(validationErrors);
      } catch (error) {
        console.error('Validation error:', error);
      }
    }
  }, [answers, formData?.form, hasAttemptedSubmit, isClient]);

  // Debug canSubmit value
  useEffect(() => {
    if (isClient && formData?.form) {
      console.log('Submit button state:', {
        canSubmit,
        progress,
        submitting,
        buttonDisabled: submitting || !canSubmit,
        answersCount: Object.keys(answers).length
      });
    }
  }, [canSubmit, progress, submitting, answers, isClient, formData?.form]);

  // Auto-save functionality
  useEffect(() => {
    if (autoSaveEnabled && formData?.form && isClient) {
      const timeoutId = setTimeout(() => {
        try {
          saveProgress();
        } catch (error) {
          console.error('Auto-save error:', error);
        }
      }, 2000); // Auto-save after 2 seconds of inactivity

      return () => clearTimeout(timeoutId);
    }
  }, [answers, autoSaveEnabled, saveProgress, formData?.form, isClient]);

  const handleSubmit = async () => {
    if (!formData?.form) return;

    // Mark that user has attempted to submit
    setHasAttemptedSubmit(true);

    try {
      // Validate all answers before submission
      const visibleQuestions = getVisibleQuestions(formData.form, answers);
      const validationErrors = validateAllAnswers(visibleQuestions, answers);

      if (Object.keys(validationErrors).length > 0) {
        setErrors(validationErrors);
        // Scroll to first error
        const firstErrorElement = document.querySelector('[data-error="true"]');
        firstErrorElement?.scrollIntoView({ behavior: 'smooth', block: 'center' });
        return;
      }

      const submission = await submitForm(answers);
      setSubmissionId(submission._id);
      setIsSubmitted(true);

      // Clear local storage after successful submission (only on client)
      if (isClient && typeof window !== 'undefined') {
        try {
          localStorage.removeItem(`form_progress_${token}`);
        } catch (error) {
          console.warn('Failed to clear localStorage:', error);
        }
      }
    } catch (error) {
      console.error('Submission failed:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="text-center"
        >
          <Icons.spinner className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600 font-medium">Loading form...</p>
        </motion.div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-md mx-auto text-center"
        >
          <Card className="shadow-lg">
            <CardContent className="p-8">
              <Icons.alertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h1 className="text-xl font-bold text-gray-900 mb-2">Form Unavailable</h1>
              <p className="text-gray-600 mb-4">{error}</p>
              <p className="text-sm text-gray-500">
                Please contact the form owner for a new link.
              </p>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    );
  }

  if (isSubmitted && submissionId) {
    return (
      <SubmissionSuccess
        organization={formData?.organization}
        submissionId={submissionId}
      />
    );
  }

  if (!formData) return null;

  const { form, organization, branding } = formData;

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Progress Bar */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-gray-200"
      >
        <div className="max-w-4xl mx-auto px-4 py-3">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Progress: {Math.round(progress)}%
            </span>
            <div className="flex items-center gap-2 text-xs text-gray-500">
              {isClient && autoSaveEnabled && (
                <>
                  <Icons.check className="h-3 w-3 text-green-500" />
                  <span>Auto-saved</span>
                </>
              )}
            </div>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </motion.div>

      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <SharedFormHeader
          form={form}
          organization={organization}
          branding={branding}
        />

        {/* Magic Link Auth Banner */}
        <AnimatePresence>
          {isClient && !autoSaveEnabled && (
            <SafeMotionDiv
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mb-8"
            >
              <Alert className="bg-blue-50 border-blue-200">
                <Icons.info className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  <div className="flex items-center justify-between">
                    <span>Want to save and resume your progress?</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowMagicLink(true)}
                      className="ml-4 border-blue-300 text-blue-700 hover:bg-blue-100"
                    >
                      Log in
                    </Button>
                  </div>
                </AlertDescription>
              </Alert>
            </SafeMotionDiv>
          )}
        </AnimatePresence>

        {/* Form Sections */}
        <div className="space-y-8">
          {form.sections.map((section, index) => (
            <FormSection
              key={section._id}
              section={section}
              answers={answers}
              errors={hasAttemptedSubmit ? errors : {}}
              onAnswerChange={updateAnswer}
              onRemoveRepeatAnswers={removeRepeatAnswers}
              sectionIndex={index}
            />
          ))}
        </div>

        {/* Submit Button */}
        <SafeMotionDiv
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-12 text-center"
        >
          <Button
            onClick={handleSubmit}
            disabled={submitting}
            size="lg"
            className="px-12 py-4 text-lg font-semibold bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {submitting ? (
              <>
                <Icons.spinner className="mr-2 h-5 w-5 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Form'
            )}
          </Button>

          {!canSubmit && (
            <p className="mt-3 text-sm text-gray-500">
              Please complete all required fields to submit (Debug: canSubmit={String(canSubmit)}, progress={progress}%)
            </p>
          )}
        </SafeMotionDiv>

        {/* Footer */}
        <SafeMotionDiv
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5 }}
          className="mt-16 text-center"
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-xs text-gray-400 font-mono tracking-wider">
              POWERED BY
            </span>
            <img
              src="/images/TX-Placeholder.png"
              alt="TractionX"
              className="h-4 w-auto"
            />
          </div>
        </SafeMotionDiv>
      </div>

      {/* Magic Link Modal */}
      <MagicLinkAuth
        isOpen={showMagicLink}
        onClose={() => setShowMagicLink(false)}
        token={token}
        onSuccess={() => {
          setShowMagicLink(false);
          // Refresh to enable auto-save
          window.location.reload();
        }}
      />
    </div>
  );
}

export function SharedFormRenderer({ token }: SharedFormRendererProps) {
  // Add additional safety checks
  if (!token || typeof token !== 'string') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Invalid Link</h1>
          <p className="text-gray-600">This sharing link is invalid or malformed.</p>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <SharedFormRendererInner token={token} />
    </ErrorBoundary>
  );
}
