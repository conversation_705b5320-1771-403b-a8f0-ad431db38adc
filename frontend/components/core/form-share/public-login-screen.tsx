"use client"

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Icons } from '@/components/icons';
import { usePublicAuth } from '@/lib/contexts/public-auth-context';

interface PublicLoginScreenProps {
  token: string;
  organizationName?: string;
  formName?: string;
  organizationLogo?: string;
  onSuccess?: () => void;
}

export function PublicLoginScreen({
  token,
  organizationName,
  formName,
  organizationLogo,
  onSuccess
}: PublicLoginScreenProps) {
  const { login } = usePublicAuth();
  const [email, setEmail] = useState('');
  const [name, setName] = useState('');
  const [loading, setLoading] = useState(false);
  const [sent, setSent] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim()) return;

    setLoading(true);
    setError('');

    try {
      await login(email.trim(), name.trim() || undefined, token);
      setSent(true);
      onSuccess?.();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send magic link');
    } finally {
      setLoading(false);
    }
  };

  const handleResend = async () => {
    if (!email.trim()) return;
    
    setLoading(true);
    setError('');

    try {
      await login(email.trim(), name.trim() || undefined, token);
      setError('');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resend magic link');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md"
      >
        <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
          <CardHeader className="text-center space-y-4 pb-6">
            {/* Organization Logo */}
            {organizationLogo && (
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="flex justify-center"
              >
                <img
                  src={organizationLogo}
                  alt={organizationName || 'Organization'}
                  className="h-12 w-auto max-w-[200px] object-contain"
                />
              </motion.div>
            )}

            {/* Title */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
            >
              <CardTitle className="text-2xl font-bold text-gray-900">
                Access Your Form
              </CardTitle>
              {organizationName && (
                <p className="text-sm text-gray-600 mt-2">
                  Invited by <span className="font-semibold">{organizationName}</span>
                </p>
              )}
              {formName && (
                <p className="text-xs text-gray-500 mt-1">
                  Form: {formName}
                </p>
              )}
            </motion.div>
          </CardHeader>

          <CardContent>
            <AnimatePresence mode="wait">
              {!sent ? (
                <motion.div
                  key="form"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3 }}
                >
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="space-y-4">
                      <p className="text-sm text-gray-600 text-center">
                        Enter your details to receive a secure access link
                      </p>

                      {/* Email Field */}
                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-sm font-medium">
                          Email Address *
                        </Label>
                        <Input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          placeholder="<EMAIL>"
                          required
                          className="h-11"
                          disabled={loading}
                        />
                      </div>

                      {/* Name Field */}
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-sm font-medium">
                          Name (Optional)
                        </Label>
                        <Input
                          id="name"
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Your full name"
                          className="h-11"
                          disabled={loading}
                        />
                      </div>
                    </div>

                    {/* Error Alert */}
                    {error && (
                      <motion.div
                        initial={{ opacity: 0, y: -10 }}
                        animate={{ opacity: 1, y: 0 }}
                      >
                        <Alert variant="destructive">
                          <Icons.alertCircle className="h-4 w-4" />
                          <AlertDescription>{error}</AlertDescription>
                        </Alert>
                      </motion.div>
                    )}

                    {/* Submit Button */}
                    <Button
                      type="submit"
                      className="w-full h-11 text-base font-medium"
                      disabled={loading || !email.trim()}
                    >
                      {loading ? (
                        <>
                          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                          Sending Link...
                        </>
                      ) : (
                        <>
                          <Icons.mail className="mr-2 h-4 w-4" />
                          Send Access Link
                        </>
                      )}
                    </Button>
                  </form>
                </motion.div>
              ) : (
                <motion.div
                  key="success"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-center space-y-4"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: 0.2, type: "spring" }}
                    className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto"
                  >
                    <Icons.mail className="h-8 w-8 text-green-600" />
                  </motion.div>

                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      Check Your Email
                    </h3>
                    <p className="text-sm text-gray-600 mb-4">
                      We've sent a secure access link to:
                    </p>
                    <p className="text-sm font-medium text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                      {email}
                    </p>
                  </div>

                  <div className="space-y-3">
                    <p className="text-xs text-gray-500">
                      The link will expire in 15 minutes for security.
                    </p>
                    
                    <Button
                      variant="outline"
                      onClick={handleResend}
                      disabled={loading}
                      className="w-full"
                    >
                      {loading ? (
                        <>
                          <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
                          Resending...
                        </>
                      ) : (
                        'Resend Link'
                      )}
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>

        {/* Footer */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6 }}
          className="mt-8 text-center"
        >
          <div className="flex items-center justify-center space-x-2">
            <span className="text-xs text-gray-400 font-mono tracking-wider">
              POWERED BY
            </span>
            <img
              src="/Logo wo space.png"
              alt="TractionX"
              className="h-4 w-auto"
            />
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
}
