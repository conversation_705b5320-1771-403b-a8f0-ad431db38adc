"use client"

import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  <PERSON>Chart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Responsive<PERSON><PERSON>r,
  <PERSON>
} from 'recharts';
import { cn } from '@/lib/utils';
import { 
  SectorData, 
  DealStageData
} from '@/lib/mock-data/dashboard';

interface ChartsSectionProps {
  sectorData: SectorData[];
  dealStagesData: DealStageData[];
  className?: string;
}

export function ChartsSection({ 
  sectorData, 
  dealStagesData, 
  className 
}: ChartsSectionProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const chartVariants = {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium">{label}</p>
          <p className="text-sm text-muted-foreground">
            {`${payload[0].name}: ${payload[0].value}`}
          </p>
        </div>
      );
    }
    return null;
  };

  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="font-medium">{payload[0].name}</p>
          <p className="text-sm text-muted-foreground">
            {`${payload[0].value}% of deals`}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6", className)}
    >
      {/* Sector Distribution Chart */}
      <motion.div variants={chartVariants} className="col-span-1">
        <Card className="h-[350px] md:h-[400px]">
          <CardHeader className="pb-2">
            <CardTitle className="text-base md:text-lg font-semibold">Sector Distribution</CardTitle>
            <p className="text-xs md:text-sm text-muted-foreground">
              Portfolio breakdown by industry sector
            </p>
          </CardHeader>
          <CardContent className="h-[250px] md:h-[300px] p-2 md:p-6">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={sectorData}
                  cx="50%"
                  cy="50%"
                  innerRadius="40%"
                  outerRadius="70%"
                  paddingAngle={2}
                  dataKey="value"
                  animationBegin={0}
                  animationDuration={1000}
                >
                  {sectorData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip content={<CustomPieTooltip />} />
                <Legend 
                  verticalAlign="bottom" 
                  height={36}
                  iconType="circle"
                  wrapperStyle={{ fontSize: '11px' }}
                  layout="horizontal"
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </motion.div>

      {/* Deal Stages Chart */}
      <motion.div variants={chartVariants} className="col-span-1">
        <Card className="h-[350px] md:h-[400px]">
          <CardHeader className="pb-2">
            <CardTitle className="text-base md:text-lg font-semibold">Deal Stages</CardTitle>
            <p className="text-xs md:text-sm text-muted-foreground">
              Active deals by funding stage
            </p>
          </CardHeader>
          <CardContent className="h-[250px] md:h-[300px] p-2 md:p-6">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart 
                data={dealStagesData} 
                margin={{ 
                  top: 10, 
                  right: 10, 
                  left: 10, 
                  bottom: 20 
                }}
              >
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="stage" 
                  tick={{ fontSize: 11 }}
                  stroke="#666"
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis 
                  tick={{ fontSize: 11 }}
                  stroke="#666"
                  width={30}
                />
                <Tooltip content={<CustomTooltip />} />
                <Bar 
                  dataKey="count" 
                  radius={[4, 4, 0, 0]}
                  animationDuration={1000}
                  animationBegin={200}
                >
                  {dealStagesData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
