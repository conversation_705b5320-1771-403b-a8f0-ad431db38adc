"use client"

import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  TrendingUp, 
  FileText, 
  Target, 
  ArrowRight,
  Plus
} from 'lucide-react';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface QuickActionsProps {
  className?: string;
}

interface ActionButton {
  title: string;
  description: string;
  href: string;
  icon: React.ReactNode;
  color: string;
  bgColor: string;
  primary?: boolean;
}

export function QuickActions({ className }: QuickActionsProps) {
  const actions: ActionButton[] = [
    {
      title: 'View Deals',
      description: 'Manage your deal pipeline',
      href: '/deals',
      icon: <TrendingUp className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      primary: true
    },
    {
      title: 'View Forms',
      description: 'Create and manage forms',
      href: '/forms',
      icon: <FileText className="h-5 w-5" />,
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50 hover:bg-emerald-100'
    },
    {
      title: 'View Theses',
      description: 'Configure investment criteria',
      href: '/theses',
      icon: <Target className="h-5 w-5" />,
      color: 'text-violet-600',
      bgColor: 'bg-violet-50 hover:bg-violet-100'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.4
      }
    }
  };

  const buttonVariants = {
    hidden: { 
      opacity: 0, 
      y: 20,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: {
        duration: 0.4,
        ease: "easeOut"
      }
    }
  };

  const hoverVariants = {
    hover: {
      scale: 1.02,
      transition: {
        duration: 0.2,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("space-y-4", className)}
    >
      {/* Section Header */}
      <motion.div variants={buttonVariants}>
        <h3 className="text-lg font-semibold text-foreground mb-2">Quick Actions</h3>
        <p className="text-sm text-muted-foreground">
          Jump to key areas of your investment workflow
        </p>
      </motion.div>

      {/* Action Buttons Grid */}
      <motion.div 
        variants={containerVariants}
        className="grid grid-cols-1 md:grid-cols-3 gap-3"
      >
        {actions.map((action, index) => (
          <motion.div
            key={action.title}
            variants={buttonVariants}
            whileHover="hover"
          >
            <Link href={action.href}>
              <motion.div variants={hoverVariants}>
                <Card className="group cursor-pointer transition-all duration-300 hover:shadow-md border-0 shadow-sm">
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <div className={cn(
                        "p-2 rounded-lg transition-colors duration-200",
                        action.bgColor
                      )}>
                        <div className={action.color}>
                          {action.icon}
                        </div>
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <h4 className="font-medium text-foreground group-hover:text-primary transition-colors">
                            {action.title}
                          </h4>
                          <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground group-hover:translate-x-1 transition-all duration-200" />
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {action.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            </Link>
          </motion.div>
        ))}
      </motion.div>

      {/* Create New Section */}
      <motion.div variants={buttonVariants} className="pt-4">
        <Card className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-950/20 dark:to-blue-950/20 border-slate-200 dark:border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-foreground mb-1">Create Something New</h4>
                <p className="text-sm text-muted-foreground">
                  Start building your investment workflow
                </p>
              </div>
              <div className="flex gap-2">
                <Link href="/forms">
                  <Button size="sm" variant="outline" className="gap-2">
                    <Plus className="h-4 w-4" />
                    New Form
                  </Button>
                </Link>
                <Link href="/theses">
                  <Button size="sm" className="gap-2">
                    <Plus className="h-4 w-4" />
                    New Thesis
                  </Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
