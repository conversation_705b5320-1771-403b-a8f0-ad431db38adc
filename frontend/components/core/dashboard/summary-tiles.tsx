"use client"

import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { TrendingUp, FileText, Target, Cpu, ArrowUpRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { DashboardStats } from '@/lib/mock-data/dashboard';
import Link from 'next/link';

interface SummaryTilesProps {
  stats: DashboardStats;
  className?: string;
}

interface TileData {
  title: string;
  value: string | number;
  trend?: {
    value: number;
    label: string;
  };
  icon: React.ComponentType<{ className?: string }>;
  href: string;
  color: string;
  bgColor: string;
}

export function SummaryTiles({ stats, className }: SummaryTilesProps) {
  const tiles: TileData[] = [
    {
      title: 'Active Deals',
      value: stats.activeDeals,
      trend: {
        value: stats.dealsTrend,
        label: 'this month'
      },
      icon: TrendingUp,
      href: '/deals',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: 'Forms',
      value: stats.forms,
      icon: FileText,
      href: '/forms',
      color: 'text-emerald-600',
      bgColor: 'bg-emerald-50'
    },
    {
      title: 'Theses',
      value: stats.theses,
      icon: Target,
      href: '/theses',
      color: 'text-violet-600',
      bgColor: 'bg-violet-50'
    },
    {
      title: 'AI Activity: Coming Soon',
      value: stats.aiActivity.status === 'active' ? 'Active' : 'Inactive',
      trend: {
        value: 0,
        label: `Last sync: ${stats.aiActivity.lastSync}`
      },
      icon: Cpu,
      href: '/dashboard',
      color: 'text-amber-600',
      bgColor: 'bg-amber-50'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const tileVariants = {
    hidden: {
      opacity: 0,
      y: 20,
      scale: 0.95
    },
    visible: {
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  };

  const CountUpNumber = ({ value, duration = 1000 }: { value: number; duration?: number }) => {
    const [displayValue, setDisplayValue] = React.useState(0);

    React.useEffect(() => {
      let startTime: number;
      let animationFrame: number;

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp;
        const progress = Math.min((timestamp - startTime) / duration, 1);

        setDisplayValue(Math.floor(progress * value));

        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate);
        }
      };

      animationFrame = requestAnimationFrame(animate);

      return () => {
        if (animationFrame) {
          cancelAnimationFrame(animationFrame);
        }
      };
    }, [value, duration]);

    return <span>{displayValue}</span>;
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className={cn("grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4", className)}
    >
      {tiles.map((tile, index) => (
        <motion.div key={tile.title} variants={tileVariants} className="h-full">
          <Link href={tile.href} className="block h-full">
            <Card className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105 border-0 shadow-sm h-full flex flex-col">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 flex-shrink-0">
                <CardTitle className="text-sm font-medium text-muted-foreground truncate pr-2">
                  {tile.title}
                </CardTitle>
                <div className={cn("p-2 rounded-lg flex-shrink-0", tile.bgColor)}>
                  <div className={tile.color}>
                    <tile.icon className="h-5 w-5" />
                  </div>
                </div>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col justify-between">
                <div className="flex items-start justify-between h-full">
                  <div className="flex-1 min-w-0">
                    <div className="text-2xl font-bold mb-2">
                      {typeof tile.value === 'number' ? (
                        <CountUpNumber value={tile.value} />
                      ) : (
                        <span className="truncate block">{tile.value}</span>
                      )}
                    </div>
                    {tile.trend && (
                      <div className="flex flex-col gap-1">
                        {tile.trend.value > 0 && (
                          <Badge variant="secondary" className="text-xs bg-emerald-100 text-emerald-700 hover:bg-emerald-100 w-fit">
                            +{tile.trend.value}%
                          </Badge>
                        )}
                        <span className="text-xs text-muted-foreground line-clamp-2">
                          {tile.trend.label}
                        </span>
                      </div>
                    )}
                  </div>
                  <ArrowUpRight className="h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors flex-shrink-0 ml-2" />
                </div>
              </CardContent>
            </Card>
          </Link>
        </motion.div>
      ))}
    </motion.div>
  );
}
