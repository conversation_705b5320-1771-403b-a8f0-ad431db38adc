"use client"

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FormWithDetails } from '@/lib/types/form';
import { FormRenderer } from './form-renderer';
import { PremiumProgressBar } from './premium-progress-bar';
import { AutoSaveManager } from './auto-save-manager';
import {
  FormAnswers,
  RepeatableAnswers,
  VisibilityContext,
  calculateProgress
} from './visibility-engine';
import { validateForm, ValidationResult } from './validation-engine';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Save,
  RotateCcw,
  CheckCircle,
  FileText,
  Zap,
  Shield,
  Star
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface FormPreviewProps {
  form: FormWithDetails;
  className?: string;
}

export function FormPreview({ form, className }: FormPreviewProps) {
  const { toast } = useToast();

  // Form state
  const [answers, setAnswers] = useState<FormAnswers>({});
  const [repeatableAnswers, setRepeatableAnswers] = useState<RepeatableAnswers>({});
  const [validationResults, setValidationResults] = useState<ValidationResult>({ isValid: true, errors: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Auto-save manager
  const [autoSaveManager] = useState(() => new AutoSaveManager(form._id || form.id!));

  // Load saved state on mount
  useEffect(() => {
    // Only run on client side
    if (typeof window !== 'undefined') {
      try {
        const savedState = autoSaveManager.loadState();
        if (savedState) {
          setAnswers(savedState.answers);
          setRepeatableAnswers(savedState.repeatableAnswers);
          setLastSaved(autoSaveManager.getLastSavedTime());

          toast({
            title: "Progress Restored",
            description: "Your previous answers have been restored.",
            duration: 3000,
          });
        }
      } catch (error) {
        console.error('Failed to load saved state:', error);
      }
    }
    setIsLoading(false);
  }, [autoSaveManager, toast]);

  // Auto-save when answers change
  useEffect(() => {
    if (!isLoading && typeof window !== 'undefined') {
      try {
        autoSaveManager.saveState(answers, repeatableAnswers);
        setLastSaved(new Date());
      } catch (error) {
        console.error('Failed to save state:', error);
      }
    }
  }, [answers, repeatableAnswers, autoSaveManager, isLoading]);

  // Create visibility context
  const visibilityContext: VisibilityContext = {
    answers,
    repeatableAnswers
  };

  // Calculate progress
  const allQuestions = form.sections.flatMap(section => section.questions);
  const progress = calculateProgress(allQuestions, visibilityContext);

  // Validate form in real-time
  useEffect(() => {
    // Get all visible questions
    const visibleQuestions = allQuestions.filter(question => {
      // For now, we'll validate all questions - visibility filtering can be added here
      return true;
    });

    const visibleQuestionIds = visibleQuestions.map(q => q._id || q.id!);
    const validation = validateForm(allQuestions, answers, visibleQuestionIds);
    setValidationResults(validation);
  }, [answers, allQuestions]);

  // Answer change handlers
  const handleAnswerChange = useCallback((questionId: string, value: any) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  }, []);

  const handleRepeatableAnswerChange = useCallback((
    sectionId: string,
    instanceIndex: number,
    questionId: string,
    value: any
  ) => {
    setRepeatableAnswers(prev => ({
      ...prev,
      [sectionId]: {
        ...prev[sectionId],
        [instanceIndex]: {
          ...prev[sectionId]?.[instanceIndex],
          [questionId]: value
        }
      }
    }));
  }, []);

  const handleAddRepeatableInstance = useCallback((sectionId: string) => {
    setRepeatableAnswers(prev => {
      const existingInstances = prev[sectionId] || {};
      const maxIndex = Math.max(...Object.keys(existingInstances).map(Number), -1);
      const newIndex = maxIndex + 1;

      return {
        ...prev,
        [sectionId]: {
          ...existingInstances,
          [newIndex]: {}
        }
      };
    });
  }, []);

  const handleRemoveRepeatableInstance = useCallback((sectionId: string, instanceIndex: number) => {
    setRepeatableAnswers(prev => {
      const newSectionAnswers = { ...prev[sectionId] };
      delete newSectionAnswers[instanceIndex];

      return {
        ...prev,
        [sectionId]: newSectionAnswers
      };
    });
  }, []);

  // Clear all data
  const handleClearForm = () => {
    setAnswers({});
    setRepeatableAnswers({});
    if (typeof window !== 'undefined') {
      try {
        autoSaveManager.clearState();
      } catch (error) {
        console.error('Failed to clear state:', error);
      }
    }
    setLastSaved(null);

    toast({
      title: "Form Cleared",
      description: "All answers have been cleared.",
      duration: 3000,
    });
  };

  // Manual save (for demonstration)
  const handleManualSave = () => {
    autoSaveManager.saveState(answers, repeatableAnswers);
    setLastSaved(new Date());

    toast({
      title: "Progress Saved",
      description: "Your answers have been saved locally.",
      duration: 3000,
    });
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading form...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("min-h-screen relative", className)}>
      {/* Premium Sticky Progress Bar */}
      <PremiumProgressBar
        completed={progress.completed}
        total={progress.total}
        percentage={progress.percentage}
        isComplete={progress.percentage === 100}
      />

      {/* Hero Header Section */}
      <div className="relative overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
          <div className="absolute inset-0 opacity-40">
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-100/20 to-cyan-100/20"></div>
          </div>
        </div>

        <div className="relative max-w-4xl mx-auto px-4 py-16 lg:py-24">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center"
          >
            {/* Premium Logo/Icon */}
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <div className="relative w-24 h-24 mx-auto">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-3xl animate-pulse"></div>
                <div className="absolute inset-1 bg-white rounded-2xl flex items-center justify-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-xl flex items-center justify-center">
                    <FileText className="h-6 w-6 text-white" />
                  </div>
                </div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                  className="absolute -inset-2 bg-gradient-to-r from-indigo-500 via-purple-500 to-cyan-500 rounded-full opacity-20 blur-lg"
                ></motion.div>
              </div>
            </motion.div>

            {/* Form Title */}
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-4xl lg:text-6xl font-extrabold tracking-tight mb-6"
            >
              <span className="bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent">
                {form.name}
              </span>
            </motion.h1>

            {/* Description */}
            {form.description && (
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
                className="text-xl text-slate-600 max-w-3xl mx-auto mb-8 leading-relaxed"
              >
                {form.description}
              </motion.p>
            )}

            {/* Premium Badges */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
              className="flex items-center justify-center gap-3 flex-wrap"
            >
              <Badge
                variant="outline"
                className="px-4 py-2 text-sm font-medium border-indigo-200 text-indigo-700 bg-indigo-50/50 backdrop-blur-sm flex items-center gap-1"
              >
                <Zap className="h-3 w-3" />
                <span>Version {form.version}</span>
              </Badge>
              {form.is_active && (
                <Badge
                  variant="default"
                  className="px-4 py-2 text-sm font-medium bg-gradient-to-r from-green-500 to-emerald-500 border-0 flex items-center gap-1"
                >
                  <Shield className="h-3 w-3" />
                  <span>Active</span>
                </Badge>
              )}
              {/* <Badge
                variant="outline"
                className="px-4 py-2 text-sm font-medium border-purple-200 text-purple-700 bg-purple-50/50 backdrop-blur-sm"
              >
                <Star className="h-3 w-3 mr-1" />
                Premium Experience
              </Badge> */}
            </motion.div>
          </motion.div>
        </div>
      </div>

      {/* Main Form Content */}
      <div className="max-w-4xl mx-auto px-4 pb-12">
        {/* Form Content */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <FormRenderer
            form={form}
            answers={answers}
            repeatableAnswers={repeatableAnswers}
            onAnswerChange={handleAnswerChange}
            onRepeatableAnswerChange={handleRepeatableAnswerChange}
            onAddRepeatableInstance={handleAddRepeatableInstance}
            onRemoveRepeatableInstance={handleRemoveRepeatableInstance}
            validationResults={validationResults}
          />
        </motion.div>

        {/* Premium Form Actions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="mt-16"
        >
          <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-xl">
            <CardContent className="p-8">
              <div className="flex flex-col lg:flex-row items-center justify-between gap-6">
                <div className="flex items-center gap-4">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={handleManualSave}
                      variant="outline"
                      size="lg"
                      className="px-6 py-3 rounded-xl border-2 border-indigo-200 text-indigo-700 hover:bg-indigo-50 hover:border-indigo-300 transition-all duration-200 flex items-center gap-2"
                    >
                      <Save className="h-5 w-5" />
                      <span>Save Progress</span>
                    </Button>
                  </motion.div>

                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={handleClearForm}
                      variant="outline"
                      size="lg"
                      className="px-6 py-3 rounded-xl border-2 border-slate-200 text-slate-700 hover:bg-slate-50 hover:border-slate-300 transition-all duration-200 flex items-center gap-2"
                    >
                      <RotateCcw className="h-5 w-5" />
                      <span>Clear Form</span>
                    </Button>
                  </motion.div>
                </div>

                <div className="text-center lg:text-right space-y-2">
                  {lastSaved && (
                    <motion.p
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      className="text-sm text-slate-500 flex items-center gap-2"
                    >
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse flex-shrink-0"></div>
                      <span>Last saved: {lastSaved.toLocaleTimeString()}</span>
                    </motion.p>
                  )}

                  <AnimatePresence>
                    {progress.percentage === 100 && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8, y: 10 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.8, y: -10 }}
                        transition={{ duration: 0.5, ease: "easeOut" }}
                        className="flex items-center gap-3 text-emerald-600"
                      >
                        <motion.div
                          animate={{ rotate: [0, 360] }}
                          transition={{ duration: 0.6, ease: "easeInOut" }}
                          className="flex items-center justify-center"
                        >
                          <CheckCircle className="h-6 w-6" />
                        </motion.div>
                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold">Form Complete!</span>
                          <motion.div
                            animate={{ scale: [1, 1.2, 1] }}
                            transition={{ duration: 1, repeat: Infinity }}
                            className="flex items-center justify-center"
                          >
                            <Star className="h-5 w-5 text-yellow-500" />
                          </motion.div>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
