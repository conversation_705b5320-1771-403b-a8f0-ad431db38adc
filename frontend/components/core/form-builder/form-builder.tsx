"use client"

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { DndContext, DragEndEvent, DragOverlay, DragStartEvent } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { toast } from '@/components/ui/use-toast';
import {
  Save,
  Plus,
  Settings,
  Eye,
  Loader2,
  AlertCircle,
  GripVertical,
  FileText,
  Layout,
  Sidebar,
  X,
  Trash2,
  Share
} from 'lucide-react';

import { FormWithDetails, Section, Question } from '@/lib/types/form';
import { FormBuilderAPI } from '@/lib/api/form-builder-api';
import FormAPI from '@/lib/api/form-api';
import { SectionCard } from './section-card';
import { FormMetaEditor } from './form-meta-editor';
import { AddSectionDialog } from './add-section-dialog';
import { FormDetailPanel } from './form-detail-panel';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';
import { ShareFormDialog } from './share-form-dialog';

interface FormBuilderProps {
  initialForm?: FormWithDetails;
  formId?: string;
}

export function FormBuilder({ initialForm, formId }: FormBuilderProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  // Check if we should focus on exclusions
  const focusExclusions = searchParams && searchParams.get('tab') === 'exclusions';

  // State
  const [form, setForm] = useState<FormWithDetails | null>(initialForm || null);
  const [isLoading, setIsLoading] = useState(!initialForm);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const [showMetaEditor, setShowMetaEditor] = useState(false);
  const [showAddSection, setShowAddSection] = useState(false);
  
  // Guard to prevent duplicate form creation in React Strict Mode
  const [formCreationAttempted, setFormCreationAttempted] = useState(false);
  const [isCreatingForm, setIsCreatingForm] = useState(false);

  // New state for enhanced UI
  const [selectedItem, setSelectedItem] = useState<{ type: 'form' | 'section' | 'question'; id: string } | null>(null);
  const [showDetailPanel, setShowDetailPanel] = useState(true);

  // Drag and drop state
  const [draggedItem, setDraggedItem] = useState<{ type: 'section' | 'question'; id: string } | null>(null);

  // New state for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // Load form data if formId is provided, or create new form if neither formId nor initialForm
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('FormBuilder useEffect triggered:', { 
        formId, 
        initialForm: !!initialForm, 
        formCreationAttempted, 
        isCreatingForm 
      });
    }
    
    if (formId && !initialForm) {
      loadForm();
    } else if (!formId && !initialForm && !formCreationAttempted && !isCreatingForm) {
      setFormCreationAttempted(true);
      createNewForm();
    }
  }, [formId, initialForm, formCreationAttempted, isCreatingForm]);

  // Auto-select form when focusing on exclusions or when form loads
  useEffect(() => {
    if (form && (!selectedItem || focusExclusions)) {
      setSelectedItem({ type: 'form', id: form._id || form.id! });
    }
  }, [focusExclusions, form, selectedItem]);

  // Cleanup effect to reset form creation guard on unmount
  useEffect(() => {
    return () => {
      setFormCreationAttempted(false);
      setIsCreatingForm(false);
    };
  }, []);

  const loadForm = async () => {
    if (!formId) return;

    try {
      setIsLoading(true);
      setError(null);
      const formData = await FormBuilderAPI.getFormDetails(formId);
      setForm(formData);
    } catch (err) {
      console.error('Error loading form:', err);
      setError('Failed to load form');
      toast({
        title: 'Error',
        description: 'Failed to load form',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createNewForm = async () => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🚀 createNewForm called');
    }
    
    // Prevent multiple simultaneous form creation attempts
    if (isCreatingForm) {
      if (process.env.NODE_ENV === 'development') {
        console.log('⚠️ Form creation already in progress, skipping...');
      }
      return;
    }

    try {
      setIsCreatingForm(true);
      setIsLoading(true);
      setError(null);

      // Create a new form with default values
      const newFormData = await FormBuilderAPI.createForm({
        name: 'Untitled Form',
        description: 'A new form to collect data from your users.',
        is_active: false
      });
      
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Form created successfully:', newFormData);
      }

      // Get the full form details with sections
      const formDetails = await FormBuilderAPI.getFormDetails(newFormData._id || newFormData.id!);
      setForm(formDetails);

      // Update the URL to include the new form ID
      router.replace(`/forms/${newFormData._id || newFormData.id}`);
    } catch (err) {
      console.error('Error creating new form:', err);
      setError('Failed to create new form');
      // Reset the guard on error so user can try again
      setFormCreationAttempted(false);
      toast({
        title: 'Error',
        description: 'Failed to create new form',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
      setIsCreatingForm(false);
    }
  };

  // Handle form metadata updates
  const handleFormUpdate = async (updates: { name?: string; description?: string; is_active?: boolean }) => {
    if (!form) return;

    try {
      setIsSaving(true);
      const updatedForm = await FormBuilderAPI.updateForm(form._id || form.id!, updates);

      setForm(prev => prev ? { ...prev, ...updates } : null);
      setHasUnsavedChanges(false);

      toast({
        title: 'Success',
        description: 'Form updated successfully'
      });
    } catch (err) {
      console.error('Error updating form:', err);
      toast({
        title: 'Error',
        description: 'Failed to update form',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Handle section operations
  const handleAddSection = async (sectionData: { title: string; description: string; repeatable: boolean }) => {
    if (!form) return;

    try {
      const newOrder = Math.max(...form.sections.map(s => s.order), 0) + 1;
      const newSection = await FormBuilderAPI.createSection(form._id || form.id!, {
        ...sectionData,
        order: newOrder
      });

      setForm(prev => prev ? {
        ...prev,
        sections: [...prev.sections, { ...newSection, questions: [] }]
      } : null);

      toast({
        title: 'Success',
        description: 'Section added successfully'
      });
    } catch (err) {
      console.error('Error adding section:', err);
      toast({
        title: 'Error',
        description: 'Failed to add section',
        variant: 'destructive'
      });
    }
  };

  const handleUpdateSection = async (sectionId: string, updates: any) => {
    try {
      const updatedSection = await FormBuilderAPI.updateSection(sectionId, updates);

      setForm(prev => prev ? {
        ...prev,
        sections: prev.sections.map(s =>
          (s._id || s.id) === sectionId ? { ...s, ...updates } : s
        )
      } : null);

      toast({
        title: 'Success',
        description: 'Section updated successfully'
      });
    } catch (err) {
      console.error('Error updating section:', err);
      toast({
        title: 'Error',
        description: 'Failed to update section',
        variant: 'destructive'
      });
    }
  };

  const handleDeleteSection = async (sectionId: string) => {
    try {
      await FormBuilderAPI.deleteSection(sectionId);

      setForm(prev => prev ? {
        ...prev,
        sections: prev.sections.filter(s => (s._id || s.id) !== sectionId)
      } : null);

      toast({
        title: 'Success',
        description: 'Section deleted successfully'
      });
    } catch (err) {
      console.error('Error deleting section:', err);
      toast({
        title: 'Error',
        description: 'Failed to delete section',
        variant: 'destructive'
      });
    }
  };

  // Handle question operations
  const handleUpdateQuestion = async (questionId: string, updates: any) => {
    try {
      console.log('🔄 Updating question via FormDetailPanel:', questionId, updates);
      await FormAPI.updateQuestion(questionId, updates);

      // Update local state
      setForm(prev => {
        if (!prev) return null;

        const updatedSections = prev.sections.map(section => ({
          ...section,
          questions: section.questions.map(q =>
            (q._id || q.id) === questionId ? { ...q, ...updates } : q
          )
        }));

        return { ...prev, sections: updatedSections };
      });

      toast({
        title: 'Success',
        description: 'Question updated successfully'
      });
    } catch (err) {
      console.error('Error updating question:', err);
      toast({
        title: 'Error',
        description: 'Failed to update question',
        variant: 'destructive'
      });
    }
  };

  const handleDuplicateSection = async (sectionId: string) => {
    if (!form) return;

    try {
      const duplicatedSection = await FormBuilderAPI.duplicateSection(form._id || form.id!, sectionId);

      // Reload form to get the complete duplicated section with questions
      await loadForm();

      toast({
        title: 'Success',
        description: 'Section duplicated successfully'
      });
    } catch (err) {
      console.error('Error duplicating section:', err);
      toast({
        title: 'Error',
        description: 'Failed to duplicate section',
        variant: 'destructive'
      });
    }
  };

  // Handle drag and drop
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    const [type, id] = active.id.toString().split('-');
    setDraggedItem({ type: type as 'section' | 'question', id });
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setDraggedItem(null);

    if (!over || active.id === over.id) return;

    const [activeType, activeId] = active.id.toString().split('-');
    const [overType, overId] = over.id.toString().split('-');

    if (activeType !== overType) return; // Can't move between different types

    setIsSaving(true);
    try {
      if (activeType === 'section') {
        // Handle section reordering
        const sections = [...form!.sections];
        const activeIndex = sections.findIndex(s => (s._id || s.id) === activeId);
        const overIndex = sections.findIndex(s => (s._id || s.id) === overId);

        if (activeIndex !== -1 && overIndex !== -1) {
          const [movedSection] = sections.splice(activeIndex, 1);
          sections.splice(overIndex, 0, movedSection);

          // Update orders
          const updatedSections = sections.map((section, index) => ({
            ...section,
            order: index
          }));

          // Update local state immediately for smooth UX
          setForm(prev => prev ? { ...prev, sections: updatedSections } : null);

          // Save new order to backend using atomic reorder API
          try {
            const reorderItems = updatedSections
              .filter(section => section._id || section.id)
              .map((section, index) => ({
                id: section._id || section.id!,
                order: index
              }));

            await FormAPI.reorderSections(reorderItems);

            toast({
              title: "Section order updated",
              description: "The section order has been saved successfully.",
            });
          } catch (apiError) {
            console.error('Error updating section order:', apiError);
            // Revert on error
            await loadForm();
            throw apiError;
          }
        }
      } else if (activeType === 'question') {
        // Handle question reordering within the same section
        // Find which section contains these questions
        let targetSection: Section | null = null;
        for (const section of form!.sections) {
          const hasActive = section.questions.some(q => (q._id || q.id) === activeId);
          const hasOver = section.questions.some(q => (q._id || q.id) === overId);

          if (hasActive && hasOver) {
            targetSection = section;
            break;
          }
        }

        if (targetSection) {
          const questions = [...targetSection.questions];
          const activeIndex = questions.findIndex(q => (q._id || q.id) === activeId);
          const overIndex = questions.findIndex(q => (q._id || q.id) === overId);

          if (activeIndex !== -1 && overIndex !== -1) {
            // Reorder questions
            const [movedQuestion] = questions.splice(activeIndex, 1);
            questions.splice(overIndex, 0, movedQuestion);

            // Update order property for all questions
            const updatedQuestions = questions.map((question, index) => ({
              ...question,
              order: index
            }));

            // Update local state immediately for smooth UX
            setForm(prev => {
              if (!prev) return null;
              const updatedSections = prev.sections.map(section =>
                (section._id || section.id) === (targetSection!._id || targetSection!.id)
                  ? { ...section, questions: updatedQuestions }
                  : section
              );
              return { ...prev, sections: updatedSections };
            });

            // Update backend using atomic reorder API
            try {
              const reorderItems = updatedQuestions
                .filter(question => question._id || question.id)
                .map((question, index) => ({
                  id: question._id || question.id!,
                  order: index
                }));

              await FormAPI.reorderQuestions(reorderItems);

              toast({
                title: "Question order updated",
                description: "The question order has been saved successfully.",
              });
            } catch (apiError) {
              console.error('Error updating question order:', apiError);
              // Revert on error
              await loadForm();
              throw apiError;
            }
          }
        }
      }
    } catch (error) {
      console.error('Error updating order:', error);
      toast({
        title: "Error",
        description: "Failed to update order. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Loading form...</p>
        </div>
      </div>
    );
  }

  if (error || !form) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-destructive mx-auto mb-4" />
          <p className="text-destructive mb-4">{error || 'Form not found'}</p>
          <Button onClick={() => router.push('/forms')} variant="outline">
            Back to Forms
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="flex h-16 items-center justify-between px-4 sm:px-6">
          <div className="flex items-center gap-3 sm:gap-4 min-w-0">
            <div className="flex items-center gap-2 min-w-0">
              <FileText className="h-5 w-5 text-primary flex-shrink-0" />
              <div className="min-w-0">
                <h1 className="text-base sm:text-lg font-semibold truncate">{form.name}</h1>
                <p className="text-xs text-muted-foreground truncate hidden sm:block">{form.description}</p>
              </div>
            </div>
            <Badge variant={form.is_active ? 'default' : 'secondary'} className="text-xs flex-shrink-0">
              {form.is_active ? 'Active' : 'Draft'}
            </Badge>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowDetailPanel(!showDetailPanel)}
              className="lg:hidden"
            >
              {showDetailPanel ? <X className="h-4 w-4" /> : <Sidebar className="h-4 w-4" />}
            </Button>
            <ShareFormDialog formId={form._id || form.id!} formName={form.name}>
              <Button
                variant="outline"
                size="sm"
                className="hidden sm:flex"
              >
                <Share className="h-4 w-4 mr-2" />
                Share
              </Button>
            </ShareFormDialog>
            <ShareFormDialog formId={form._id || form.id!} formName={form.name}>
              <Button
                variant="outline"
                size="sm"
                className="sm:hidden"
              >
                <Share className="h-4 w-4" />
              </Button>
            </ShareFormDialog>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetailPanel(!showDetailPanel)}
              className="hidden sm:flex"
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetailPanel(!showDetailPanel)}
              className="sm:hidden"
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/preview/${form._id || form.id}`)}
              className="hidden sm:flex"
            >
              <Eye className="h-4 w-4 mr-2" />
              Preview
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push(`/preview/${form._id || form.id}`)}
              className="sm:hidden"
            >
              <Eye className="h-4 w-4" />
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => setDeleteDialogOpen(true)}
              className="ml-2"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Form
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content - Independent Scrolling Layout */}
      <div className="flex-1 flex overflow-hidden">
        <DndContext onDragStart={handleDragStart} onDragEnd={handleDragEnd}>
          {/* Form Structure Panel - Left Column */}
          <div className={`flex flex-col transition-all duration-300 ${showDetailPanel ? 'flex-1 lg:w-1/2' : 'w-full'} min-w-0`}>
            {/* Header - Fixed */}
            <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
              <div className="px-6 py-6">
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <div className="flex items-center gap-3">
                    <Layout className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <h2 className="text-lg font-semibold">Form Structure</h2>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant="outline" className="text-xs">
                          {form.sections.length} section{form.sections.length !== 1 ? 's' : ''}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {form.sections.reduce((total, section) => total + section.questions.length, 0)} question{form.sections.reduce((total, section) => total + section.questions.length, 0) !== 1 ? 's' : ''}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddSection(true)}
                    className="w-full sm:w-auto h-10 min-h-10 min-w-10 touch-manipulation"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Scrollable Content */}
            <div className="flex-1 overflow-y-auto max-h-screen">
              <div className="px-6 py-6 space-y-4">
                <SortableContext
                  items={form.sections.map(s => `section-${s._id || s.id}`)}
                  strategy={verticalListSortingStrategy}
                >
                  <AnimatePresence>
                    {form.sections
                      .sort((a, b) => a.order - b.order)
                      .map((section) => (
                        <motion.div
                          key={section._id || section.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: -20 }}
                          transition={{ duration: 0.2 }}
                        >
                          <SectionCard
                            section={section}
                            form={form}
                            isActive={activeSection === (section._id || section.id)}
                            isSelected={selectedItem?.type === 'section' && selectedItem.id === (section._id || section.id)}
                            selectedQuestionId={selectedItem?.type === 'question' ? selectedItem.id : undefined}
                            onUpdate={(updates) => handleUpdateSection(section._id || section.id!, updates)}
                            onDelete={() => handleDeleteSection(section._id || section.id!)}
                            onDuplicate={() => handleDuplicateSection(section._id || section.id!)}
                            onToggleActive={() => setActiveSection(
                              activeSection === (section._id || section.id) ? null : (section._id || section.id!)
                            )}
                            onSelect={() => {
                              setSelectedItem({ type: 'section', id: section._id || section.id! });
                              setShowDetailPanel(true);
                            }}
                            onSelectQuestion={(questionId) => {
                              setSelectedItem({ type: 'question', id: questionId });
                              setShowDetailPanel(true);
                              // Auto-expand the section containing this question
                              setActiveSection(section._id || section.id!);
                            }}
                            onRefreshForm={loadForm}
                          />
                        </motion.div>
                      ))}
                  </AnimatePresence>
                </SortableContext>

                {/* Empty State */}
                {form.sections.length === 0 && (
                  <Card className="border-dashed border-2 border-muted-foreground/25 rounded-xl shadow-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16 sm:py-20 text-center px-6">
                      <Layout className="h-12 w-12 text-muted-foreground/50 mb-6" />
                      <h3 className="font-semibold text-lg sm:text-xl mb-3">No sections yet</h3>
                      <p className="text-muted-foreground mb-8 max-w-md text-sm sm:text-base leading-relaxed">
                        Start building your form by adding your first section. Sections help organize your questions into logical groups.
                      </p>
                      <Button
                        onClick={() => setShowAddSection(true)}
                        size="lg"
                        className="h-12 px-6 rounded-full min-h-10 min-w-10 touch-manipulation"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>
          </div>

          {/* Settings Panel - Right Column */}
          <AnimatePresence>
            {showDetailPanel && (
              <>
                {/* Desktop Settings Panel */}
                <motion.div
                  initial={{ x: 300, opacity: 0 }}
                  animate={{ x: 0, opacity: 1 }}
                  exit={{ x: 300, opacity: 0 }}
                  transition={{ type: "spring", damping: 30, stiffness: 300 }}
                  className="hidden lg:flex flex-col w-1/2 border-l bg-muted/30"
                >
                  {/* Settings Header - Fixed */}
                  <div className="flex-shrink-0 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                    <div className="flex items-center justify-between px-6 py-4">
                      <h3 className="text-lg font-semibold">Settings</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setShowDetailPanel(false)}
                        className="h-8 w-8 p-0 hover:bg-muted"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Settings Content - Scrollable */}
                  <div className="flex-1 overflow-y-auto max-h-screen">
                    <FormDetailPanel
                      form={form}
                      selectedItem={selectedItem || { type: 'form', id: form._id || form.id! }}
                      onUpdateForm={handleFormUpdate}
                      onUpdateSection={handleUpdateSection}
                      onUpdateQuestion={handleUpdateQuestion}
                      onClose={() => setShowDetailPanel(false)}
                    />
                  </div>
                </motion.div>

                {/* Mobile Settings Panel - Full Screen Modal */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="lg:hidden fixed inset-0 z-40 bg-black/20 backdrop-blur-sm"
                  onClick={() => setShowDetailPanel(false)}
                />

                <motion.div
                  initial={{ y: "100%" }}
                  animate={{ y: 0 }}
                  exit={{ y: "100%" }}
                  transition={{ type: "spring", damping: 30, stiffness: 300 }}
                  className="lg:hidden fixed inset-x-0 bottom-0 z-50 bg-background border-t shadow-2xl rounded-t-2xl"
                >
                  <div className="max-h-[90vh] flex flex-col">
                    {/* Mobile Header with Handle and Close */}
                    <div className="flex-shrink-0 border-b bg-background/95">
                      <div className="flex justify-center py-3">
                        <div className="w-12 h-1.5 bg-muted-foreground/40 rounded-full" />
                      </div>
                      <div className="flex items-center justify-between px-6 pb-4">
                        <h3 className="text-lg font-semibold">Settings</h3>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setShowDetailPanel(false)}
                          className="h-10 w-10 p-0 hover:bg-muted touch-manipulation"
                        >
                          <X className="h-5 w-5" />
                        </Button>
                      </div>
                    </div>

                    {/* Mobile Content - Scrollable */}
                    <div className="flex-1 overflow-y-auto">
                      <FormDetailPanel
                        form={form}
                        selectedItem={selectedItem || { type: 'form', id: form._id || form.id! }}
                        onUpdateForm={handleFormUpdate}
                        onUpdateSection={handleUpdateSection}
                        onUpdateQuestion={handleUpdateQuestion}
                        onClose={() => setShowDetailPanel(false)}
                      />
                    </div>
                  </div>
                </motion.div>
              </>
            )}
          </AnimatePresence>

          {/* Drag Overlay */}
          <DragOverlay>
            {draggedItem && (
              <Card className="opacity-90 rotate-3 shadow-lg border-primary/50">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <GripVertical className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">
                      {draggedItem.type === 'section' ? 'Section' : 'Question'}
                    </span>
                  </div>
                </CardContent>
              </Card>
            )}
          </DragOverlay>
        </DndContext>
      </div>

      {/* Dialogs */}
      <FormMetaEditor
        form={form}
        open={showMetaEditor}
        onOpenChange={setShowMetaEditor}
        onSave={handleFormUpdate}
        isSaving={isSaving}
      />

      <AddSectionDialog
        open={showAddSection}
        onOpenChange={setShowAddSection}
        onAdd={handleAddSection}
      />

      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Form"
        description={`Are you sure you want to delete the form "${form.name}"? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={async () => {
          setDeleting(true);
          try {
            const id = String(form._id || form.id);
            await FormBuilderAPI.deleteForm(id);
            toast({
              title: 'Form deleted',
              description: 'The form has been deleted successfully.'
            });
            router.push('/forms');
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete form. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
          }
        }}
        loading={deleting}
      />
    </div>
  );
}
