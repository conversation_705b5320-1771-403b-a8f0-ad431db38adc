# Exclusion Filter Block

The Exclusion Filter Block is a comprehensive UI component for managing exclusion rules in the Form Builder. It allows users to define conditions that will flag form submissions as potentially out-of-scope.

## Features

- **Visual Condition Builder**: Intuitive interface for creating exclusion rules
- **Question-Type Aware**: Operators and value inputs adapt based on question type
- **Real-time Validation**: Immediate feedback on condition validity
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Framer Motion Animations**: Smooth transitions for adding/removing conditions
- **Backend Integration**: Full CRUD operations with the exclusion filter API

## Components

### ExclusionFilterBlock
Main container component that manages the overall state and API interactions.

**Props:**
- `form: Form` - The form object containing sections and questions
- `className?: string` - Optional CSS classes

**Features:**
- Loads existing exclusion filters for the form
- Manages local condition state with change tracking
- <PERSON><PERSON> saving/updating filters via API
- Shows validation errors and success messages

### ConditionBuilder
Individual condition editor component.

**Props:**
- `condition: ConditionBuilderState` - Current condition state
- `questions: Question[]` - Available questions for selection
- `onUpdate: (condition: ConditionBuilderState) => void` - Update callback
- `onRemove: () => void` - Remove callback
- `showRemove?: boolean` - Whether to show remove button

**Features:**
- Question selector with type information
- Operator selector filtered by question type
- Dynamic value input based on question type and operator
- Real-time validation with error display
- Condition preview for valid rules

### OperatorSelector
Dropdown for selecting comparison operators.

**Features:**
- Filters operators based on question type
- Shows operator descriptions
- Handles unsupported question types gracefully

### ValueInput
Dynamic input component that adapts to question type and operator.

**Supported Input Types:**
- Text input for short/long text questions
- Number input for numeric questions
- Date picker for date questions
- Boolean toggle for boolean questions
- Single/multi-select for select questions
- Multi-value input for IN/NOT_IN operators

## Usage

### In Form Builder
The exclusion filter block is integrated as a tab in the main form builder:

```tsx
<Tabs>
  <TabsList>
    <TabsTrigger value="builder">Form Builder</TabsTrigger>
    <TabsTrigger value="exclusions">Exclusion Filters</TabsTrigger>
    <TabsTrigger value="preview">Preview</TabsTrigger>
  </TabsList>
  
  <TabsContent value="exclusions">
    <ExclusionFilterBlock form={form} />
  </TabsContent>
</Tabs>
```

### Direct Link from Forms List
Users can access exclusion filters directly from the forms list via the three-dot menu:

```tsx
<DropdownMenuItem asChild>
  <Link href={`/forms/${formId}?tab=exclusions`}>
    <Filter className="h-4 w-4 mr-2" />
    Edit Exclusion Filters
  </Link>
</DropdownMenuItem>
```

## API Integration

The component integrates with the backend exclusion filter API:

- **GET** `/exclusion-filters?form_id={id}` - List filters for a form
- **POST** `/exclusion-filters` - Create new filter
- **PUT** `/exclusion-filters/{id}` - Update existing filter
- **DELETE** `/exclusion-filters/{id}` - Delete filter

## Supported Operators

The system supports the following operators based on question type:

- **Text Questions**: equals, not equals, contains, not contains
- **Number Questions**: equals, not equals, greater than, less than, >=, <=
- **Select Questions**: equals, not equals, in, not in
- **Boolean Questions**: equals, not equals
- **Date Questions**: equals, not equals, greater than, less than, >=, <=

## Validation

The component provides comprehensive validation:

- **Required Fields**: Question, operator, and value must be selected
- **Operator Compatibility**: Only valid operators for question type are shown
- **Value Format**: Values are validated based on question type
- **Missing Questions**: Warns if referenced questions are deleted

## Styling

The component follows the Thesis Builder aesthetic:
- Card-based layout with ample white space
- Color-coded validation (green/red borders)
- Consistent with ShadCN UI design system
- Responsive grid layouts
- Smooth animations with Framer Motion

## Error Handling

- Network errors are caught and displayed as toast notifications
- Invalid conditions are highlighted with error messages
- Missing or deleted questions show warning badges
- Graceful fallbacks for API failures

## Future Enhancements

- Drag-and-drop reordering of conditions
- Condition groups with nested AND/OR logic
- Import/export of exclusion rule templates
- Bulk operations for multiple forms
- Advanced condition types (regex, custom functions)
