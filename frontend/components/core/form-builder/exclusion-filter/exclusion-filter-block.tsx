"use client"

import React, { useState, useEffect } from 'react';
import { Plus, Save, AlertTriangle, Filter, Trash2 } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';

import { ConditionBuilder } from './condition-builder';
import { Form, Question } from '@/lib/types/form';
import {
  ExclusionFilter,
  ExclusionFilterCreateRequest,
  ExclusionFilterUpdateRequest,
  ExclusionCondition,
  LogicalOperator,
  ConditionBuilderState
} from '@/lib/types/exclusion-filter';
import { ExclusionFilterAPI } from '@/lib/api/exclusion-filter-api';

interface ExclusionFilterBlockProps {
  form: Form;
  className?: string;
}

export function ExclusionFilterBlock({ form, className }: ExclusionFilterBlockProps) {
  const [filters, setFilters] = useState<ExclusionFilter[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [localConditions, setLocalConditions] = useState<ConditionBuilderState[]>([]);
  const [hasChanges, setHasChanges] = useState(false);
  const [currentFilter, setCurrentFilter] = useState<ExclusionFilter | null>(null);

  // Get all questions from all sections for condition building
  const allQuestions: Question[] = React.useMemo(() => {
    return form.sections.flatMap(section => section.questions || []);
  }, [form.sections]);

  // Load existing exclusion filters for this form
  useEffect(() => {
    const loadFilters = async () => {
      if (!form._id && !form.id) {
        console.log('No form ID available, skipping filter load');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const formId = form._id || form.id!;
        console.log('Loading exclusion filters for form ID:', formId);

        const existingFilters = await ExclusionFilterAPI.listExclusionFilters(formId);
        console.log('Loaded exclusion filters:', existingFilters);
        setFilters(existingFilters);

        // If there's an active filter, load its conditions
        const activeFilter = existingFilters.find(f => f.is_active);
        if (activeFilter) {
          console.log('Found active filter:', activeFilter);
          setCurrentFilter(activeFilter);
          setLocalConditions(
            activeFilter.conditions.map(condition => ({
              question_id: condition.question_id,
              operator: condition.operator,
              value: condition.value,
              isValid: true
            }))
          );
        } else {
          console.log('No active filter found');
        }
      } catch (error) {
        console.error('Error loading exclusion filters:', error);
        toast({
          title: 'Error',
          description: `Failed to load exclusion filters: ${error instanceof Error ? error.message : 'Unknown error'}`,
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    };

    loadFilters();
  }, [form._id, form.id]);

  // Handle adding a new condition
  const addCondition = () => {
    const newCondition: ConditionBuilderState = {
      question_id: '',
      operator: 'eq' as any,
      value: '',
      isValid: false
    };
    setLocalConditions([...localConditions, newCondition]);
    setHasChanges(true);
  };

  // Handle updating a condition
  const updateCondition = (index: number, condition: ConditionBuilderState) => {
    const updated = [...localConditions];
    updated[index] = condition;
    setLocalConditions(updated);
    setHasChanges(true);
  };

  // Handle removing a condition
  const removeCondition = (index: number) => {
    const updated = localConditions.filter((_, i) => i !== index);
    setLocalConditions(updated);
    setHasChanges(true);
  };

  // Handle deactivating all filters
  const deactivateAllFilters = async () => {
    try {
      setSaving(true);

      if (currentFilter) {
        // Update existing filter to be inactive with no conditions
        const updateRequest: ExclusionFilterUpdateRequest = {
          conditions: [],
          is_active: false
        };

        await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
        setCurrentFilter(null);
        setFilters([]);
      }

      // Clear local state
      setLocalConditions([]);
      setHasChanges(false);

      toast({
        title: 'Success',
        description: 'All exclusion filters have been deactivated',
      });
    } catch (error) {
      console.error('Error deactivating filters:', error);
      toast({
        title: 'Error',
        description: 'Failed to deactivate exclusion filters',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  // Handle saving exclusion filters
  const saveFilters = async () => {
    if (!form._id && !form.id) {
      toast({
        title: 'Error',
        description: 'Form must be saved before adding exclusion filters',
        variant: 'destructive'
      });
      return;
    }

    try {
      setSaving(true);
      const formId = form._id || form.id!;

      // Validate conditions
      const validConditions = localConditions.filter(c => c.isValid);

      // Convert to API format
      const conditions: ExclusionCondition[] = validConditions.map(c => ({
        question_id: c.question_id,
        operator: c.operator,
        value: c.value
      }));

      // If no valid conditions, deactivate the filter
      if (conditions.length === 0) {
        if (currentFilter) {
          const updateRequest: ExclusionFilterUpdateRequest = {
            conditions: [],
            is_active: false
          };

          await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
          setCurrentFilter(null);
          setFilters([]);
        }

        setLocalConditions([]);
        setHasChanges(false);

        toast({
          title: 'Success',
          description: 'Exclusion filters have been deactivated (no valid conditions)',
        });
        return;
      }

      if (currentFilter) {
        // Update existing filter
        const updateRequest: ExclusionFilterUpdateRequest = {
          conditions,
          operator: LogicalOperator.OR, // Default to OR logic
          is_active: true
        };

        const updated = await ExclusionFilterAPI.updateExclusionFilter(currentFilter._id!, updateRequest);
        setCurrentFilter(updated);

        // Update filters list
        setFilters(prev => prev.map(f => f._id === updated._id ? updated : f));
      } else {
        // Create new filter
        const createRequest: ExclusionFilterCreateRequest = {
          form_id: formId,
          name: `${form.name} Exclusion Rules`,
          description: 'Automatically generated exclusion rules for this form',
          operator: LogicalOperator.OR,
          conditions,
          is_active: true
        };

        const created = await ExclusionFilterAPI.createExclusionFilter(createRequest);
        setCurrentFilter(created);
        setFilters(prev => [...prev, created]);
      }

      setHasChanges(false);
      toast({
        title: 'Success',
        description: 'Exclusion filters saved successfully',
      });
    } catch (error) {
      console.error('Error saving exclusion filters:', error);
      toast({
        title: 'Error',
        description: 'Failed to save exclusion filters',
        variant: 'destructive'
      });
    } finally {
      setSaving(false);
    }
  };

  // Get invalid conditions for warning display
  const invalidConditions = localConditions.filter(c => !c.isValid);
  const hasInvalidConditions = invalidConditions.length > 0;

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Exclusion Filters
          </CardTitle>
          <CardDescription>Loading exclusion filters...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Exclusion Filters
              {localConditions.length > 0 && (
                <Badge variant="secondary">
                  {localConditions.length} rule{localConditions.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              Deals will be flagged as excluded if any of these conditions are true
            </CardDescription>
          </div>
          <div className="flex gap-2">
            {(localConditions.length > 0 || currentFilter) && (
              <Button
                variant="ghost"
                size="sm"
                onClick={deactivateAllFilters}
                disabled={saving}
                className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
              >
                {saving ? 'Deactivating...' : 'Deactivate All'}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={saveFilters}
              disabled={saving || !hasChanges || hasInvalidConditions}
            >
              {saving ? 'Saving...' : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Warning for invalid conditions */}
        {hasInvalidConditions && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {invalidConditions.length} condition{invalidConditions.length !== 1 ? 's' : ''} {invalidConditions.length === 1 ? 'is' : 'are'} incomplete.
              Please complete all conditions before saving.
            </AlertDescription>
          </Alert>
        )}

        {/* Empty state */}
        {localConditions.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            <Filter className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium mb-2">No exclusion rules set</p>
            <p className="text-sm mb-4">Add your first exclusion rule to automatically flag deals that don't meet your criteria.</p>
            <Button onClick={addCondition} variant="outline">
              <Plus className="mr-2 h-4 w-4" />
              Add exclusion rule
            </Button>
          </div>
        )}

        {/* Conditions list */}
        <AnimatePresence>
          {localConditions.map((condition, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <ConditionBuilder
                condition={condition}
                questions={allQuestions}
                onUpdate={(updated) => updateCondition(index, updated)}
                onRemove={() => removeCondition(index)}
                showRemove={true}
              />
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Add condition button */}
        {localConditions.length > 0 && (
          <>
            <Separator />
            <Button
              variant="outline"
              onClick={addCondition}
              className="w-full"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add another exclusion rule
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );
}
