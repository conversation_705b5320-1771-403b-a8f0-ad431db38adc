"use client"

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Shield,
  Plus,
  Trash2,
  AlertTriangle,
  Save,
  Loader2,
  Eye,
  EyeOff,
  <PERSON>tings,
  Edit,
  Filter,
  X
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

import { FormWithDetails, Question } from '@/lib/types/form';
import {
  ExclusionFilter,
  ExclusionCondition,
  ExclusionOperator,
  LogicalOperator,
  getSupportedOperators,
  getOperatorInfo
} from '@/lib/types/exclusion-filter';
import { ExclusionFilterAPI } from '@/lib/api/exclusion-filter-api';

interface ExclusionFilterBlockProps {
  form: FormWithDetails;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

export function ExclusionFilterBlock({
  form,
  isCollapsed = false,
  onToggleCollapse
}: ExclusionFilterBlockProps) {
  const [filters, setFilters] = useState<ExclusionFilter[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingFilter, setEditingFilter] = useState<ExclusionFilter | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [filterToDelete, setFilterToDelete] = useState<ExclusionFilter | null>(null);
  const [deleting, setDeleting] = useState(false);

  // Form state
  const [filterName, setFilterName] = useState('');
  const [filterDescription, setFilterDescription] = useState('');
  const [logicalOperator, setLogicalOperator] = useState<LogicalOperator>(LogicalOperator.OR);
  const [isActive, setIsActive] = useState(true);
  const [localConditions, setLocalConditions] = useState<ExclusionCondition[]>([]);

  // Get all questions from the form for condition building
  const allQuestions: Question[] = form.sections.flatMap(section => section.questions);

  useEffect(() => {
    loadExclusionFilters();
  }, [form._id]);

  const loadExclusionFilters = async () => {
    try {
      setIsLoading(true);
      const formFilters = await ExclusionFilterAPI.listExclusionFilters(form._id || form.id);
      setFilters(formFilters || []);
    } catch (error) {
      console.error('Error loading exclusion filters:', error);
      toast({
        title: 'Error',
        description: 'Failed to load exclusion filters',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const resetFormState = () => {
    setFilterName('');
    setFilterDescription('');
    setLogicalOperator(LogicalOperator.OR);
    setLocalConditions([]);
    setIsActive(true);
    setEditingFilter(null);
    setShowCreateForm(false);
  };

  const handleCreate = () => {
    resetFormState();
    setShowCreateForm(true);
  };

  const handleEdit = (filter: ExclusionFilter) => {
    setEditingFilter(filter);
    setFilterName(filter.name);
    setFilterDescription(filter.description || '');
    setLogicalOperator(filter.operator);
    setLocalConditions(filter.conditions || []);
    setIsActive(filter.is_active);
    setShowCreateForm(true);
  };

  const handleCancel = () => {
    resetFormState();
  };

  const handleSave = async () => {
    if (!filterName.trim()) {
      toast({
        title: 'Validation Error',
        description: 'Filter name is required',
        variant: 'destructive'
      });
      return;
    }

    // Validate conditions if any exist
    if (localConditions.length > 0) {
      const invalidConditions = localConditions.filter(
        condition => !condition.question_id || !condition.value
      );

      if (invalidConditions.length > 0) {
        toast({
          title: 'Validation Error',
          description: 'All conditions must have a question and value',
          variant: 'destructive'
        });
        return;
      }
    }

    try {
      setIsSaving(true);

      const filterData = {
        form_id: form._id || form.id!,
        name: filterName,
        description: filterDescription,
        operator: logicalOperator,
        conditions: localConditions,
        is_active: isActive && localConditions.length > 0
      };

      if (editingFilter) {
        await ExclusionFilterAPI.updateExclusionFilter(editingFilter._id || editingFilter.id!, filterData);
        toast({
          title: 'Success',
          description: 'Exclusion filter updated successfully'
        });
      } else {
        await ExclusionFilterAPI.createExclusionFilter(filterData);
        toast({
          title: 'Success',
          description: 'Exclusion filter created successfully'
        });
      }

      await loadExclusionFilters();
      resetFormState();
    } catch (error) {
      console.error('Error saving exclusion filter:', error);
      toast({
        title: 'Error',
        description: 'Failed to save exclusion filter',
        variant: 'destructive'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteClick = (filter: ExclusionFilter) => {
    setFilterToDelete(filter);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!filterToDelete) return;
    
    setDeleting(true);
    try {
      const filterId = String(filterToDelete._id || filterToDelete.id);
      await ExclusionFilterAPI.deleteExclusionFilter(filterId);
      await loadExclusionFilters();
      
      // If we were editing this filter, reset the form
      if (editingFilter && (editingFilter._id === filterToDelete._id || editingFilter.id === filterToDelete.id)) {
        resetFormState();
      }
      
      toast({
        title: 'Filter deleted',
        description: 'The exclusion filter has been deleted successfully.'
      });
    } catch (error) {
      console.error('Error deleting exclusion filter:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete exclusion filter',
        variant: 'destructive'
      });
    } finally {
      setDeleting(false);
      setDeleteDialogOpen(false);
      setFilterToDelete(null);
    }
  };

  const handleAddCondition = () => {
    const newCondition: ExclusionCondition = {
      question_id: '',
      operator: ExclusionOperator.EQUALS,
      value: ''
    };
    setLocalConditions([...localConditions, newCondition]);
  };

  const handleUpdateCondition = (index: number, field: keyof ExclusionCondition, value: any) => {
    const updatedConditions = [...localConditions];
    updatedConditions[index] = { ...updatedConditions[index], [field]: value };
    setLocalConditions(updatedConditions);
  };

  const handleRemoveCondition = (index: number) => {
    const updatedConditions = localConditions.filter((_, i) => i !== index);
    setLocalConditions(updatedConditions);
  };

  const getQuestionById = (questionId: string): Question | undefined => {
    return allQuestions.find(q => (q._id || q.id) === questionId);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading exclusion filters...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-amber-200">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Shield className="h-5 w-5 text-amber-600" />
            <div>
              <CardTitle className="text-lg">Exclusion Filters</CardTitle>
              <CardDescription>
                Configure filters to exclude certain submissions
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Button onClick={handleCreate} size="sm" variant="ghost" className="p-2">
              <Plus className="h-4 w-4 text-black" />
            </Button>
          </div>
        </div>
      </CardHeader>

      {!isCollapsed && (
        <CardContent className="space-y-6">
          {/* Existing Filters List */}
          {filters.length > 0 && (
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900">Existing Filters</h4>
              {filters.map((filter) => (
                <Card key={filter._id || filter.id} className="border-slate-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h5 className="font-medium">{filter.name}</h5>
                          <Badge variant={filter.is_active ? "default" : "secondary"}>
                            {filter.is_active ? "Active" : "Inactive"}
                          </Badge>
                          <Badge variant="outline">
                            {filter.conditions.length} rule{filter.conditions.length !== 1 ? 's' : ''}
                          </Badge>
                        </div>
                        {filter.description && (
                          <p className="text-sm text-slate-600 mb-2">{filter.description}</p>
                        )}
                        {filter.conditions.length > 0 && (
                          <div className="text-xs text-slate-500">
                            {filter.conditions.map((condition, index) => {
                              const question = getQuestionById(condition.question_id);
                              const operatorInfo = getOperatorInfo(condition.operator);
                              return (
                                <span key={index}>
                                  {question?.label} {operatorInfo?.label || 'equals'} "{condition.value}"
                                  {index < filter.conditions.length - 1 && ` ${filter.operator.toUpperCase()} `}
                                </span>
                              );
                            })}
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(filter)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDeleteClick(filter)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Create/Edit Form */}
          {showCreateForm && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="border-t pt-6"
            >
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-medium text-slate-900">
                  {editingFilter ? 'Edit Filter' : 'Create New Filter'}
                </h4>
                <Button variant="ghost" size="sm" onClick={handleCancel}>
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-4">
                {/* Filter Configuration */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="filter-name">Filter Name</Label>
                    <Input
                      id="filter-name"
                      value={filterName}
                      onChange={(e) => setFilterName(e.target.value)}
                      placeholder="e.g., Geographic Exclusions"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filter-description">Description</Label>
                    <Input
                      id="filter-description"
                      value={filterDescription}
                      onChange={(e) => setFilterDescription(e.target.value)}
                      placeholder="Optional description"
                    />
                  </div>
                </div>

                {/* Logic Operator */}
                {localConditions.length > 1 && (
                  <div className="space-y-2">
                    <Label>Logic</Label>
                    <Select
                      value={logicalOperator}
                      onValueChange={(value) => setLogicalOperator(value as LogicalOperator)}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value={LogicalOperator.OR}>
                          OR - Flag if ANY condition matches
                        </SelectItem>
                        <SelectItem value={LogicalOperator.AND}>
                          AND - Flag only if ALL conditions match
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Active Toggle */}
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Active Filter</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable this filter to flag matching submissions
                    </p>
                  </div>
                  <Switch
                    checked={isActive}
                    onCheckedChange={setIsActive}
                  />
                </div>

                <Separator />

                {/* Exclusion Rules */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Exclusion Rules</Label>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAddCondition}
                      className="mt-2"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Condition
                    </Button>
                  </div>

                  {localConditions.length === 0 ? (
                    <div className="text-center py-8 text-slate-500">
                      <Filter className="h-8 w-8 mx-auto mb-2 opacity-50" />
                      <p className="text-sm">No exclusion rules defined</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {localConditions.map((condition, index) => {
                        const question = getQuestionById(condition.question_id);
                        const supportedOperators = question ? getSupportedOperators(question.type) : [];

                        return (
                          <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-lg border border-amber-200 mb-2">
                                  {/* Question Selection */}
                            <div className="flex-1 min-w-0">
                                    <Select
                                      value={condition.question_id}
                                      onValueChange={(value) => handleUpdateCondition(index, 'question_id', value)}
                                    >
                                      <SelectTrigger>
                                        <SelectValue placeholder="Select question" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        {allQuestions.map((q) => (
                                          <SelectItem key={q._id || q.id} value={q._id || q.id!}>
                                      <div className="flex flex-col items-start">
                                        <span className="font-medium text-sm">{q.label}</span>
                                              <span className="text-xs text-muted-foreground">{q.type}</span>
                                            </div>
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>

                                  {/* Operator Selection */}
                            <div className="flex-shrink-0">
                                    <Select
                                      value={condition.operator}
                                      onValueChange={(value) => handleUpdateCondition(index, 'operator', value as ExclusionOperator)}
                                      disabled={!question}
                                    >
                                <SelectTrigger className="w-[60px]">
                                  <SelectValue>
                                    {(() => {
                                      const op = supportedOperators.find(o => o.value === condition.operator);
                                      return op ? (
                                        <span className="font-mono text-lg font-semibold">{op.symbol}</span>
                                      ) : '=';
                                    })()}
                                  </SelectValue>
                                      </SelectTrigger>
                                      <SelectContent>
                                        {supportedOperators.map((op) => (
                                          <SelectItem key={op.value} value={op.value}>
                                      <div className="flex items-center gap-2">
                                        <span className="font-mono text-sm">{op.symbol}</span>
                                        <span className="text-sm">{op.label}</span>
                                            </div>
                                          </SelectItem>
                                        ))}
                                      </SelectContent>
                                    </Select>
                                  </div>

                                  {/* Value Input */}
                            <div className="flex-shrink-0">
                                    {question?.type === 'single_select' && question.options ? (
                                      <Select
                                        value={condition.value}
                                        onValueChange={(value) => handleUpdateCondition(index, 'value', value)}
                                      >
                                  <SelectTrigger className="w-[120px]">
                                          <SelectValue placeholder="Select value" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          {question.options.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                              {option.label}
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    ) : (
                                      <Input
                                        placeholder="Enter value"
                                        value={condition.value}
                                        onChange={(e) => handleUpdateCondition(index, 'value', e.target.value)}
                                        type={question?.type === 'number' ? 'number' : 'text'}
                                  className="w-[120px]"
                                      />
                                    )}
                                </div>

                                {/* Remove Button */}
                            <div className="flex-shrink-0">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleRemoveCondition(index)}
                                  className="text-destructive hover:text-destructive"
                                >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Remove Condition
                                </Button>
                              </div>
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>

                {/* Save/Cancel Buttons */}
                <div className="flex items-center gap-2 pt-4">
                  <Button onClick={handleSave} disabled={isSaving}>
                    {isSaving ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    {editingFilter ? 'Update Filter' : 'Create Filter'}
                  </Button>
                  <Button variant="outline" onClick={handleCancel}>
                    Cancel
                  </Button>
                </div>
              </div>
            </motion.div>
          )}

          {/* Empty State */}
          {filters.length === 0 && !showCreateForm && (
            <div className="text-center py-8 text-slate-500">
              <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="text-lg font-medium mb-2">No exclusion filters</p>
              <p className="text-sm mb-4">Create your first exclusion filter to automatically flag submissions that don't meet your criteria.</p>
              <Button onClick={handleCreate} variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Create First Filter
              </Button>
            </div>
          )}
        </CardContent>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Exclusion Filter"
        description={`Are you sure you want to delete "${filterToDelete?.name}"? This action cannot be undone.`}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setFilterToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        loading={deleting}
      />
    </Card>
  );
}
