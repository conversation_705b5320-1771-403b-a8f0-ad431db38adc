import { Metadata } from "next"
import { PlaceholderScreen } from "@/components/placeholder-screen"
import { siteConfig } from "@/config/site"

export const metadata: Metadata = {
  title: {
    default: siteConfig.name,
    template: `%s | ${siteConfig.name}`,
  },
  description: siteConfig.description,
}

export default function HomePage() {
  return (
    <PlaceholderScreen
      title="TractionX - Coming Soon"
      description="Our startup investing platform is under development. We're building tools to help you track deals, score startups, and manage investment theses."
      imagePath="/images/TX-Placeholder.png"
      buttonText="Go to Dashboard"
      buttonLink="/dashboard"
    />
  )
}
