"use client"

import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { FormPreview } from '@/components/core/form-preview/form-preview';
import { FormWithDetails } from '@/lib/types/form';
import FormAPI from '@/lib/api/form-api';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export default function FormPreviewPage() {
  const params = useParams();
  const router = useRouter();
  const [form, setForm] = useState<FormWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const formId = params?.form_id as string;

  useEffect(() => {
    const fetchForm = async () => {
      if (!formId) {
        setError('Form ID is missing');
        setLoading(false);
        return;
      }

      try {
        console.log(`Fetching form details for preview: ${formId}`);
        setLoading(true);

        // Use the details endpoint to get complete form structure
        const formData = await FormAPI.getFormWithDetails(formId);
        console.log('Form data fetched for preview:', formData);

        setForm(formData);
        setError(null);
      } catch (error) {
        console.error('Error fetching form for preview:', error);
        setError('Failed to load form. It may not exist or you may not have permission to view it.');
      } finally {
        setLoading(false);
      }
    };

    if (formId) {
      fetchForm();
    }
  }, [formId]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50 flex items-center justify-center">
        <div className="flex flex-col items-center gap-6">
          <div className="relative">
            <div className="w-20 h-20 bg-gradient-to-r from-indigo-500 to-cyan-500 rounded-2xl animate-pulse"></div>
            <Loader2 className="h-8 w-8 animate-spin text-white absolute inset-0 m-auto" />
          </div>
          <div className="text-center space-y-2">
            <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent">
              Loading Form
            </h2>
            <p className="text-slate-600 animate-pulse">Preparing your premium experience...</p>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || !form) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Form not found'}
            </AlertDescription>
          </Alert>
          <div className="mt-4 text-center">
            <button
              onClick={() => router.back()}
              className="text-primary hover:text-primary/80 font-medium"
            >
              ← Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Render the form preview
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-cyan-50">
      <FormPreview form={form} />
    </div>
  );
}
