"use client"

import { useState } from "react"
import { DashboardHeader } from "@/components/header"
import { DashboardShell } from "@/components/shell"
import {
  SummaryTiles,
  ChartsSection,
  ActivityFeed,
  ShareThesisBlock,
  OnboardingFlow,
  QuickActions
} from "@/components/core/dashboard"
import {
  mockDashboardStats,
  mockSectorData,
  mockDealStagesData,
  mockActivityData,
  mockSharingData
} from "@/lib/mock-data/dashboard"

// Note: All authentication logic is preserved in the layout and auth context
// This component only handles UI rendering - no auth functions are removed

export default function DashboardPage() {
  const [showOnboarding, setShowOnboarding] = useState(false)

  // TODO: Replace with real API calls when backend is ready
  const hasData = mockDashboardStats.activeDeals > 0 || mockDashboardStats.forms > 0 || mockDashboardStats.theses > 0

  return (
    <DashboardShell>
      <DashboardHeader
        heading="Dashboard"
        text="AI-powered investment intelligence at your fingertips"
      />

      <div className="space-y-6 md:space-y-8">
        {/* Show onboarding for empty state */}
        {!hasData && (
          <OnboardingFlow
            onDismiss={() => setShowOnboarding(false)}
          />
        )}

        {/* Summary Tiles */}
        <SummaryTiles stats={mockDashboardStats} />

        {/* Charts Section */}
        <ChartsSection
          sectorData={mockSectorData}
          dealStagesData={mockDealStagesData}
        />

        {/* Activity Feed and Share Block */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6">
          <div className="lg:col-span-2">
            <ActivityFeed activities={mockActivityData} />
          </div>
          <div className="lg:col-span-1">
            <ShareThesisBlock
              shareUrl={mockSharingData.shareUrl}
              formTitle={mockSharingData.formTitle}
            />
          </div>
        </div>

        {/* Quick Actions */}
        <QuickActions />
      </div>
    </DashboardShell>
  )
}
