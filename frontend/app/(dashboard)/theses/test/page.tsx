"use client"

import React, { useState } from 'react';
import { Shell } from "@/components/shell"
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, AlertTriangle, Play, RefreshCw } from 'lucide-react';
import { MinimalThesisBuilder } from "@/components/core/thesis-builder/minimal-thesis-builder"
import { ThesisBuilder } from "@/components/core/thesis-builder/thesis-builder"
import { useAuth } from "@/lib/auth-context"

type TestMode = 'minimal' | 'full' | 'none';

export default function ThesisTestPage() {
  const { isAuthenticated } = useAuth();
  const [testMode, setTestMode] = useState<TestMode>('none');
  const [testResults, setTestResults] = useState<string[]>([]);

  if (!isAuthenticated) {
    return null;
  }

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  const testMinimal = () => {
    addTestResult('Testing minimal thesis builder...');
    setTestMode('minimal');
  };

  const testFull = () => {
    addTestResult('Testing full thesis builder...');
    setTestMode('full');
  };

  const resetTest = () => {
    addTestResult('Resetting test...');
    setTestMode('none');
  };

  return (
    <Shell>
      <div className="space-y-6">
        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Thesis Builder Crash Test
            </CardTitle>
            <CardDescription>
              Test different versions to identify and fix crashes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4">
              <Button 
                onClick={testMinimal}
                variant={testMode === 'minimal' ? 'default' : 'outline'}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Test Minimal Builder
              </Button>
              
              <Button 
                onClick={testFull}
                variant={testMode === 'full' ? 'default' : 'outline'}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                Test Full Builder
              </Button>
              
              <Button 
                onClick={resetTest}
                variant="secondary"
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Reset
              </Button>
              
              <Button 
                onClick={clearResults}
                variant="outline"
                size="sm"
              >
                Clear Log
              </Button>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm font-medium">Current Test:</span>
              <Badge variant={testMode === 'none' ? 'outline' : 'default'}>
                {testMode === 'minimal' ? 'Minimal Builder' : 
                 testMode === 'full' ? 'Full Builder' : 
                 'No Test Running'}
              </Badge>
            </div>

            {/* Test Results Log */}
            {testResults.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Test Log:</h4>
                <div className="bg-muted p-3 rounded-lg max-h-32 overflow-y-auto">
                  {testResults.map((result, index) => (
                    <div key={index} className="text-xs font-mono">
                      {result}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status Alerts */}
        {testMode === 'minimal' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Minimal builder loaded successfully! This means the basic React structure works.
            </AlertDescription>
          </Alert>
        )}

        {testMode === 'full' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Full builder loaded successfully! The infinite loop issue has been fixed.
            </AlertDescription>
          </Alert>
        )}

        {/* Component Under Test */}
        {testMode === 'minimal' && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Minimal Thesis Builder Test</h2>
            <MinimalThesisBuilder 
              onSaveSuccess={() => addTestResult('Minimal builder save successful!')}
            />
          </div>
        )}

        {testMode === 'full' && (
          <div>
            <h2 className="text-xl font-semibold mb-4">Full Thesis Builder Test</h2>
            <ThesisBuilder 
              onSaveSuccess={() => addTestResult('Full builder save successful!')}
            />
          </div>
        )}

        {testMode === 'none' && (
          <Card>
            <CardContent className="py-8">
              <div className="text-center text-muted-foreground">
                <AlertTriangle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                <p className="font-medium">No test running</p>
                <p className="text-sm">Select a test above to begin</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </Shell>
  );
}
