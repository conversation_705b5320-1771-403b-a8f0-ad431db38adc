"use client"

import React, { useState, useEffect, useCallback } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Loader2, <PERSON>ertCircle, Trash2 } from 'lucide-react';

import { Shell } from "@/components/shell"
import { ThesisBuilder } from "@/components/core/thesis-builder"
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { ThesisAPI } from '@/lib/api/thesis-api';
import { ThesisWithRules } from '@/lib/types/thesis';
import { useAuth } from '@/lib/auth-context';
import { ConfirmDialog } from '@/components/ui/confirm-dialog';

export default function EditThesisPage() {
  const params = useParams();
  const router = useRouter();
  const thesisId = params?.id as string;
  const { isAuthenticated } = useAuth();

  const [thesis, setThesis] = useState<ThesisWithRules | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [deleting, setDeleting] = useState(false);

  console.log('📝 EditThesisPage - thesisId:', thesisId);

  // Load thesis data
  useEffect(() => {
    const loadThesis = async () => {
      if (!thesisId) {
        setError('No thesis ID provided');
        setIsLoading(false);
        return;
      }

      try {
        console.log('📝 Loading thesis data for ID:', thesisId);
        setIsLoading(true);
        setError(null);

        const thesisData = await ThesisAPI.getThesis(thesisId);
        setThesis(thesisData);
        console.log('📝 Thesis data loaded:', thesisData);

      } catch (error) {
        console.error('❌ Error loading thesis:', error);
        setError(error instanceof Error ? error.message : 'Failed to load thesis');
        toast({
          title: "Error loading thesis",
          description: "Failed to load thesis data. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && thesisId) {
      loadThesis();
    }
  }, [thesisId, isAuthenticated]);

  // Handle successful save
  const handleSaveSuccess = useCallback((savedThesis: ThesisWithRules) => {
    console.log('✅ Thesis saved successfully:', savedThesis);
    setThesis(savedThesis);
    toast({
      title: "Thesis updated",
      description: "Your investment thesis has been updated successfully.",
    });
  }, []);

  if (!isAuthenticated) {
    return null; // This will be handled by middleware
  }

  if (isLoading) {
    return (
      <Shell>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto" />
            <p className="text-muted-foreground">Loading thesis data...</p>
          </div>
        </div>
      </Shell>
    );
  }

  if (error || !thesis) {
    return (
      <Shell>
        <div className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Edit Thesis</h1>
            <p className="text-muted-foreground">
              Update your investment thesis configuration
            </p>
          </div>

          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error || 'Thesis not found'}
            </AlertDescription>
          </Alert>

          <div className="flex gap-2">
            <Button onClick={() => router.back()} variant="outline">
              Go Back
            </Button>
            <Button onClick={() => router.push('/theses')}>
              View All Theses
            </Button>
          </div>
        </div>
      </Shell>
    );
  }

  return (
    <Shell>
      <div className="flex justify-end mb-4">
        <Button
          variant="destructive"
          size="sm"
          onClick={() => setDeleteDialogOpen(true)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Thesis"
        description={`Are you sure you want to delete this thesis? This action cannot be undone.`}
        onCancel={() => setDeleteDialogOpen(false)}
        onConfirm={async () => {
          setDeleting(true);
          try {
            await ThesisAPI.deleteThesis(thesisId);
            toast({
              title: 'Thesis deleted',
              description: 'The thesis has been deleted successfully.'
            });
            router.push('/theses');
          } catch (error) {
            toast({
              title: 'Error',
              description: 'Failed to delete thesis. Please try again.',
              variant: 'destructive'
            });
          } finally {
            setDeleting(false);
            setDeleteDialogOpen(false);
          }
        }}
        loading={deleting}
      />
      <ThesisBuilder
        initialThesis={thesis}
        onSaveSuccess={handleSaveSuccess}
      />
    </Shell>
  );
}
