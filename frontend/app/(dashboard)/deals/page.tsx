"use client"

import { useState, useEffect, useMemo } from "react"
import { Shell } from "@/components/shell"
import { DealsGrid, DealsHeader } from "@/components/core/deals"
import { DealAPI } from "@/lib/api/deal-api"
import { Deal } from "@/lib/types/deal"
import { useAuth } from "@/lib/auth-context"
import { mockDeals } from "@/lib/mock-data/deals"

export default function DealsPage() {
  const { isAuthenticated } = useAuth()
  const [allDeals, setAllDeals] = useState<Deal[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [activeFilter, setActiveFilter] = useState('all')

  useEffect(() => {
    const fetchDeals = async () => {
      if (!isAuthenticated) {
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        // Try to fetch from API first
        const response = await DealAPI.listDeals(0, 100)
        setAllDeals(response.deals || [])
      } catch (err: any) {
        console.error('Error fetching deals:', err)

        // Fallback to mock data for demo
        console.log('Using mock data for demo')
        const mockDealsData: Deal[] = mockDeals.map(mock => ({
          id: mock.id,
          org_id: 'demo-org',
          form_id: 'demo-form',
          submission_ids: ['demo-submission'],
          company_name: mock.company_name,
          stage: mock.stage,
          sector: mock.sector,
          status: mock.status,
          created_by: 'demo-user',
          created_at: mock.created_at,
          updated_at: mock.updated_at
        }))
        setAllDeals(mockDealsData)
        setError(null) // Don't show error when using mock data
      } finally {
        setLoading(false)
      }
    }

    fetchDeals()
  }, [isAuthenticated])

  // Filter and search deals
  const filteredDeals = useMemo(() => {
    let filtered = allDeals

    // Apply status filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(deal => deal.status === activeFilter)
    }

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(deal =>
        deal.company_name?.toLowerCase().includes(query) ||
        (Array.isArray(deal.sector)
          ? deal.sector.some(s => s.toLowerCase().includes(query))
          : deal.sector?.toLowerCase().includes(query)) ||
        deal.stage?.toLowerCase().includes(query)
      )
    }

    return filtered
  }, [allDeals, activeFilter, searchQuery])

  const handleSearchChange = (search: string) => {
    setSearchQuery(search)
  }

  const handleFilterChange = (filter: string) => {
    setActiveFilter(filter)
  }

  return (
    <Shell className="max-w-none">
      <div className="space-y-8">
        <DealsHeader
          onSearchChange={handleSearchChange}
          onFilterChange={handleFilterChange}
          activeFilter={activeFilter}
          totalDeals={allDeals.length}
        />

        <DealsGrid
          deals={filteredDeals}
          loading={loading}
          error={error}
        />
      </div>
    </Shell>
  )
}
