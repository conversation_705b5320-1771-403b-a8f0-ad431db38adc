import { Metada<PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { Suspense } from "react"

import { cn } from "@/lib/utils"
import { buttonVariants } from "@/components/ui/button"
import { Icons } from "@/components/icons"
import { CustomAuthForm } from "@/components/custom-auth-form"

export const metadata: Metadata = {
  title: "Login",
  description: "Login to your account",
}

export default function LoginPage() {
  return (
    <div className="container flex h-screen w-screen flex-col items-center justify-center">
      <Link
        href="/"
        className={cn(
          buttonVariants({ variant: "ghost" }),
          "absolute left-4 top-4 md:left-8 md:top-8"
        )}
      >
        <>
          <Icons.chevronLeft className="mr-2 h-4 w-4" />
          Back
        </>
      </Link>
      <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
        <div className="flex flex-col space-y-2 text-center">
          <div className="mx-auto mb-4">
            <Image
              src="/images/TX-Placeholder.png"
              alt="TractionX Logo"
              width={20}
              height={20}
              priority
            />
          </div>
          <h1 className="text-2xl font-semibold tracking-tight">
            Welcome back
          </h1>
          <p className="text-sm text-muted-foreground">
            Enter your credentials to access your account
          </p>
        </div>
        <Suspense fallback={<div>Loading...</div>}>
          <CustomAuthForm />
        </Suspense>
        <p className="px-8 text-center text-sm text-muted-foreground">
          <Link
            href="/register"
            className="hover:text-brand underline underline-offset-4"
          >
            Don&apos;t have an account? Sign Up
          </Link>
        </p>

        {/* Direct dashboard link for testing */}
        <div className="mt-4 text-center">
          <Link
            href="/dashboard"
            className="text-sm text-primary hover:underline"
          >
            Go directly to dashboard (if already logged in)
          </Link>
        </div>
      </div>
    </div>
  )
}
