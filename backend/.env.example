# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=X-App
VERSION=0.1.0
DESCRIPTION=Investing intelligence platform backend

# Security
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7
ALGORITHM=HS256
TEST_MODE=False
INVITATION_TOKEN_EXPIRY=900
PASSWORD_RESET_TOKEN_EXPIRY=900
ALLOWED_ORIGINS=http://localhost:8000

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000", "http://localhost:8080"]

# MongoDB
MONGODB_URL=mongodb://localhost:27017
MONGODB_DB_NAME=x_app

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_URL=redis://localhost:6379/0
REDIS_KEY_PREFIX=tx
REDIS_DEFAULT_TTL=3600

# Queue
WORKER_CONCURRENCY=2

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Reset token expiry
RESET_TOKEN_EXPIRY=120

# Organization Settings
DOMAIN=tractionx.ai
ALLOWED_HOSTS=["localhost","127.0.0.1","tractionx.ai"]

# Portal URLs
BASIC_PORTAL_URL=https://app.tractionx.com
FRONTEND_URL=https://app.tractionx.com

# Email Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your_app_password
SMTP_FROM=<EMAIL>
SMTP_FROM_NAME=TractionX

# Resend Email Service (optional - set USE_RESEND=true to use)
RESEND_API_KEY=re_your_resend_api_key_here
USE_RESEND=false