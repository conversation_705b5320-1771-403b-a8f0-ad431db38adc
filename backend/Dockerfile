FROM python:3.11-slim

WORKDIR /app

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    PYTHONPATH=/app \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_IN_PROJECT=true \
    POETRY_VIRTUALENVS_CREATE=true \
    POETRY_NO_INTERACTION=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    libpq-dev \
    zlib1g-dev \
    libjpeg-dev \
    libpng-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry - use pip for a more reliable installation
RUN pip install --no-cache-dir "poetry==$POETRY_VERSION" && \
    poetry --version

# Copy pyproject.toml and poetry.lock first for better layer caching
COPY pyproject.toml poetry.lock* README.md ./

# Create directories for queue handlers
RUN mkdir -p app/services/queue/handlers

# Install dependencies
RUN pip install --upgrade pip && \
    poetry config virtualenvs.in-project true && \
    poetry install --no-root --no-interaction --no-ansi

# Copy application code
COPY . .

# Run the application
CMD ["poetry", "run", "uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]