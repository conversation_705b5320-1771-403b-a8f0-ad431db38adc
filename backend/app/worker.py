"""
Worker script for processing jobs from the queue.

This script starts a worker pool that processes jobs from the queue.
It can be run as a separate process from the main application.

Example:
    $ python -m app.worker  # Run directly
    $ python -m app.worker --watch  # Run with hot reloading
"""

import asyncio
import sys
import signal
import os
from pathlib import Path
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

from app.services.factory import get_queue_service
from app.services.queue import QueueType, QueueFactory
from app.services.queue.handlers import HANDLERS
from app.core.logging import configure_logging, get_logger
from app.core.config import settings

# Configure logging
configure_logging()
logger = get_logger(__name__)

class WorkerFileHandler(FileSystemEventHandler):
    """Handle file system events for worker hot reloading."""
    
    def __init__(self, callback):
        self.callback = callback
        self.last_modified = {}  # Track last modified times to prevent duplicate events
    
    def on_modified(self, event):
        if event.is_directory or not event.src_path.endswith('.py'):
            return
            
        # Get current modification time
        current_mtime = Path(event.src_path).stat().st_mtime
        
        # Check if this is a duplicate event (some editors trigger multiple events)
        if event.src_path in self.last_modified and current_mtime == self.last_modified[event.src_path]:
            return
            
        self.last_modified[event.src_path] = current_mtime
        logger.info(f"Detected change in {event.src_path}")
        self.callback()

async def run_worker():
    """Run the worker process."""
    logger.info("Starting worker pool")
    
    # Create queue service
    queue_service = await get_queue_service()
    await queue_service.initialize()
    
    # Create worker pool
    worker_pool = QueueFactory.create_worker_pool(
        queue_service=queue_service,
        queue_types=[QueueType.DEFAULT, QueueType.HIGH_PRIORITY],
        poll_interval=5.0,
        shutdown_timeout=60
    )
    
    # Register handlers
    for job_type, handler in HANDLERS.items():
        worker_pool.register_handler(job_type, handler)
    
    # Initialize worker pool
    await worker_pool.initialize()
    
    # Determine number of workers
    worker_count = int(settings.WORKER_CONCURRENCY)
    logger.info("Starting worker pool", worker_count=worker_count)
    
    # Start worker pool
    await worker_pool.start(num_workers=worker_count)
    
    try:
        # Keep the worker pool running
        while True:
            await asyncio.sleep(1)
    except KeyboardInterrupt:
        logger.info("Shutting down worker pool")
    finally:
        # Shutdown worker pool
        await worker_pool.stop()
        await queue_service.cleanup()

async def watch_and_restart():
    """Watch files and restart worker when changes are detected."""
    if not os.getenv("WATCHDOG_ENABLED"):
        logger.warning("Watchdog not enabled, running without hot reload")
        await run_worker()
        return

    process = None
    restart_event = asyncio.Event()
    
    async def start_worker():
        nonlocal process
        if process:
            logger.info("Stopping existing worker process")
            process.send_signal(signal.SIGINT)
            try:
                await asyncio.wait_for(process.wait(), timeout=5)
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
        
        logger.info("Starting new worker process")
        process = await asyncio.create_subprocess_exec(
            sys.executable, "-m", "app.worker", "--no-watch",
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.STDOUT
        )
        
        asyncio.create_task(pipe_output(process))
    
    async def pipe_output(proc):
        """Pipe subprocess output to console."""
        while True:
            line = await proc.stdout.readline()
            if not line:
                break
            print(line.decode().strip())
    
    # Setup file watching
    queue_dir = Path("app/services/queue")
    if not queue_dir.exists():
        logger.error("Queue directory not found")
        return
        
    # Create and start the observer
    observer = Observer()
    handler = WorkerFileHandler(lambda: restart_event.set())
    
    # Watch the queue directory recursively
    observer.schedule(handler, str(queue_dir), recursive=True)
    
    # Also watch the worker file itself if in development
    if settings.DEBUG:
        worker_file = Path(__file__)
        observer.schedule(handler, str(worker_file.parent), recursive=False)
    
    observer.start()
    logger.info("File watching started", 
                watched_dirs=[str(queue_dir), str(Path(__file__).parent) if settings.DEBUG else None])
    
    try:
        # Start initial worker
        await start_worker()
        
        # Main loop
        while True:
            # Wait for file change
            await restart_event.wait()
            restart_event.clear()
            
            # Restart worker
            await start_worker()
            
    except KeyboardInterrupt:
        observer.stop()
        if process:
            process.terminate()
            await process.wait()
    finally:
        observer.join()

async def main():
    """Main entry point."""
    if "--watch" in sys.argv and "--no-watch" not in sys.argv:
        await watch_and_restart()
    else:
        await run_worker()

if __name__ == "__main__":
    asyncio.run(main())
