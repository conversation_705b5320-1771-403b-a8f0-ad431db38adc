from typing import Optional, <PERSON><PERSON>
from fastapi import Request, HTTPException, status
from app.models.user import User
from app.utils.common import ObjectIdField
from bson import ObjectId
from app.core.config import settings


import logging


class OrgContext:
    """
    World-class Organization context manager for multi-tenant support.
    Enforces security, clarity, and extensibility. TEST_MODE logic is respected.
    """
    logger = logging.getLogger("OrgContext")

    @staticmethod
    def extract_org_id_from_subdomain(request: Request) -> Optional[str]:
        """
        Extract organization ID from subdomain.
        Handles both development (localhost) and production domains.
        """
        host = request.headers.get("host", "")
        if not host:
            OrgContext.logger.warning(
                "No host header found for org context extraction.")
            return None
        # Development: client1.localhost:8000
        # Production: client1.tractionx.ai
        parts = host.split(".")
        if ("localhost" in host or "127.0.0.1" in host) and len(parts) > 1:
            OrgContext.logger.info(
                f"Extracted org subdomain in dev: {parts[0]}")
            return parts[0]
        elif len(parts) > 2:
            OrgContext.logger.info(
                f"Extracted org subdomain in prod: {parts[0]}")
            return parts[0]
        OrgContext.logger.warning("Could not extract org_id from subdomain.")
        return None

    @staticmethod
    def get_org_id(
        request: Request,
        user: User
    ) -> Tuple[str, bool]:
        """
        Get organization ID from various sources in order of priority:
        1. X-ORG-ID header (required for org-scoped requests except in TEST_MODE)
        2. Subdomain
        3. User's default org_id

        Returns:
            Tuple[str, bool]: (organization_id, is_cross_org)
        Raises:
            HTTPException: if org_id is missing, invalid, or user lacks access
        """
        # TEST_MODE: allow all org_id for rapid dev/testing
        if settings.TEST_MODE:
            # Get org_id from header if available
            header_org_id = request.headers.get("X-ORG-ID")
            if header_org_id:
                OrgContext.logger.info(
                    f"[TEST_MODE] Org context bypassed for org_id from header: {header_org_id}")
                return header_org_id, True

            # Fallback to user's default org
            if user and user.default_org_id:
                return str(user.default_org_id), False

        # 1. Enforce X-ORG-ID header for all org-scoped requests
        header_org_id = request.headers.get("X-ORG-ID")
        if header_org_id:
            if not ObjectId.is_valid(header_org_id):
                OrgContext.logger.error(
                    f"Invalid X-ORG-ID format: {header_org_id}")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid organization ID format (X-ORG-ID)"
                )
            if not settings.TEST_MODE and header_org_id != str(user.org_id):
                OrgContext.logger.warning(
                    f"User {user.id} tried to access forbidden org: {header_org_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You don't have access to this organization (X-ORG-ID)"
                )
            # TODO: Optionally check if org is deleted/disabled here
            OrgContext.logger.info(
                f"Using org_id from X-ORG-ID: {header_org_id}")
            return header_org_id, header_org_id != user.org_id

        # 2. Subdomain fallback (for browser-based multi-tenant routing)
        subdomain_org_id = OrgContext.extract_org_id_from_subdomain(request)
        if subdomain_org_id:
            if not settings.TEST_MODE and subdomain_org_id != str(user.org_id):
                OrgContext.logger.warning(
                    f"User {user.id} tried to access forbidden org via subdomain: {subdomain_org_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="You don't have access to this organization (subdomain)"
                )
            OrgContext.logger.info(
                f"Using org_id from subdomain: {subdomain_org_id}")
            return subdomain_org_id, subdomain_org_id != user.org_id

        # 3. No longer using explicit org_id param - rely on header or user's default org

        # 4. Default to user's primary org (if any)
        if hasattr(user, "org_id") and user.org_id:
            # Ensure org_id is returned as a string
            org_id_str = str(user.org_id) if isinstance(user.org_id, ObjectId) else user.org_id
            OrgContext.logger.info(
                f"Falling back to user's default org_id: {org_id_str}")
            return org_id_str, False

        # 5. Fail fast: org context is required
        OrgContext.logger.error(f"Org context missing for user {user.id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Organization context (X-ORG-ID) is required for this request."
        )

    @staticmethod
    def validate_org_access(user: User, org_id: str) -> bool:
        """Validate if user has access to the organization."""
        if settings.TEST_MODE:
            return True
        return org_id == str(user.org_id)

    @staticmethod
    def format_org_id(org_id: str) -> str:
        """Format organization ID consistently."""
        # If it's already an ObjectId string, return as is
        if ObjectIdField.is_valid(org_id):
            return str(ObjectIdField(org_id))
        return org_id
