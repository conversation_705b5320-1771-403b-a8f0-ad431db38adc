from typing import Async<PERSON>enerator

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from app.core.config import settings


class Database:
    client: AsyncIOMotorClient = None
    db: AsyncIOMotorDatabase = None

    async def connect_to_database(self) -> None:
        """Create database connection."""
        self.client = AsyncIOMotorClient(settings.mongodb_connection_string)
        self.db = self.client[settings.MONGODB_DB_NAME]

    async def close_database_connection(self) -> None:
        """Close database connection."""
        if self.client:
            self.client.close()

    async def get_database(self) -> AsyncGenerator[AsyncIOMotorDatabase, None]:
        """Get database instance."""
        if self.db is None:
            await self.connect_to_database()
        yield self.db


db = Database()


async def get_database() -> AsyncGenerator[AsyncIOMotorDatabase, None]:
    """FastAPI dependency that provides a database session."""
    async for database in db.get_database():
        yield database
