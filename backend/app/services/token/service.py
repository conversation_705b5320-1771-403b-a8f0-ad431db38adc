from datetime import datetime
from typing import Optional, Dict, Any
from jose import jwt
from jose.exceptions import <PERSON><PERSON><PERSON><PERSON>r
from fastapi import HTT<PERSON>Ex<PERSON>, status
from bson import ObjectId

from motor.motor_asyncio import AsyncIOMotorDatabase
from app.core.config import settings
from app.core.security import create_access_token, create_refresh_token
from app.models.token import TokenType, Token
from app.services.token.interface import ITokenService
from app.core.logging import get_logger
from app.core.auth_exceptions import (
    TokenExpiredError,
    InvalidTokenError,
    TokenTypeError
)

logger = get_logger(__name__)


class TokenService(ITokenService):
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.tokens = self.db.tokens

    async def initialize(self) -> None:
        """Initialize the service."""
        pass

    async def cleanup(self) -> None:
        """Cleanup the service."""
        pass

    async def create_token(
        self,
        user_id: str,
        token_type: TokenType,
        tenant: str,
        tier: str,
        expires_in: int,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Create a new token."""
        try:
            # Calculate timestamps
            now = int(datetime.utcnow().timestamp())
            exp_time = now + expires_in

            # Log token creation details for debugging
            logger.info(f"Creating token: type={token_type}, expires_in={expires_in}, now={now}, exp_time={exp_time}")

            # Ensure user_id is a string, not ObjectId
            if isinstance(user_id, ObjectId):
                user_id = str(user_id)

            # Process metadata to ensure all ObjectId values are converted to strings
            processed_metadata = {}
            if metadata:
                for k, v in metadata.items():
                    if isinstance(v, ObjectId):
                        processed_metadata[k] = str(v)
                    else:
                        processed_metadata[k] = v

            # Create token model with Unix timestamps
            token_model = Token(
                sub=user_id,
                tenant=tenant,
                tier=tier,
                exp=exp_time,
                iat=now,
                jti=str(ObjectId()),
                type=token_type,
                metadata=processed_metadata,
                is_revoked=False,
                created_at=now,
                updated_at=now,
                last_used_at=now
            )

            # Create JWT token
            token_data = token_model.to_claims()

            # Create token based on type
            if token_type == TokenType.ACCESS:
                token = create_access_token(token_data)
            elif token_type == TokenType.REFRESH:
                token = create_refresh_token(token_data)
            else:
                token = jwt.encode(
                    token_data,
                    settings.SECRET_KEY,
                    algorithm=settings.ALGORITHM
                )

            # Store token in model
            token_model.token = token
            await token_model.save()

            return token

        except Exception as e:
            logger.error(f"Token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create token"
            )

    async def verify_token(self, token: str, token_type: TokenType) -> Token:
        """Verify and decode a token."""
        try:
            logger.info(f"Verifying token: {token}")
            logger.info(f"Token type: {token_type}")

            # First check if token exists and is valid in database
            try:
                # Get the raw document from MongoDB first
                now = int(datetime.utcnow().timestamp())
                token_doc = await self.tokens.find_one({
                    "token": token,
                    "type": token_type,
                    "is_revoked": False,
                    "exp": {"$gt": now}
                })

                if not token_doc:
                    logger.warning(f"Token not found or expired: {token[:10]}...")
                    raise InvalidTokenError(token_preview=token[:10])

                # Manually convert ObjectId fields to strings before creating Token object
                if isinstance(token_doc.get("sub"), ObjectId):
                    token_doc["sub"] = str(token_doc["sub"])
                if isinstance(token_doc.get("jti"), ObjectId):
                    token_doc["jti"] = str(token_doc["jti"])

                # Convert metadata ObjectIds to strings
                if token_doc.get("metadata"):
                    for k, v in token_doc["metadata"].items():
                        if isinstance(v, ObjectId):
                            token_doc["metadata"][k] = str(v)

                # Create Token object from document
                stored_token = Token(**token_doc)

                logger.info(f"Found valid token: sub={stored_token.sub}, jti={stored_token.jti}, type={stored_token.type}")

            except InvalidTokenError:
                # Pass through our custom exception
                raise
            except Exception as e:
                logger.error(f"Error finding token: {str(e)}")
                raise InvalidTokenError(reason=str(e), token_preview=token[:10])

            # Double-check if token is expired (redundant with DB query but good for safety)
            if stored_token.is_expired():
                logger.warning(f"Token expired: {token[:10]}...")
                raise TokenExpiredError(token_preview=token[:10])

            try:
                # Verify JWT token
                payload = jwt.decode(
                    token,
                    settings.SECRET_KEY,
                    algorithms=[settings.ALGORITHM],
                    options={
                        "verify_exp": True,
                        "verify_iat": True,
                        "require": ["exp", "iat", "sub", "type"]
                    }
                )

                # Log the token payload for debugging
                logger.info(f"Token payload: exp={payload.get('exp')}, iat={payload.get('iat')}, type={payload.get('type')}")

                # Verify token type matches
                if payload.get("type") != token_type:
                    raise TokenTypeError(
                        expected_type=token_type,
                        actual_type=payload.get("type", "unknown")
                    )

                # Update last used timestamp
                now = int(datetime.utcnow().timestamp())
                await self.tokens.update_one(
                    {"_id": ObjectId(stored_token.id)},
                    {"$set": {"last_used_at": now}}
                )
                stored_token.last_used_at = now

                return stored_token

            except jwt.ExpiredSignatureError:
                raise TokenExpiredError(token_preview=token[:10])
            except JWTError as e:
                raise InvalidTokenError(reason=str(e), token_preview=token[:10])

        except (TokenExpiredError, InvalidTokenError, TokenTypeError):
            # Pass through our custom exceptions
            raise
        except Exception as e:
            logger.error(f"Token verification error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify token"
            )

    async def revoke_token(self, token: str) -> None:
        """Revoke a token."""
        try:
            result = await self.tokens.update_one(
                {"token": token},
                {"$set": {"is_revoked": True}}
            )
            if result.modified_count == 0:
                logger.warning(
                    f"Attempt to revoke non-existent token: {token[:10]}... (this may be normal if token was already revoked)")
                # Don't raise an exception - this is normal behavior during refresh
                # The token might have already been revoked or might not exist
            else:
                logger.info(f"Successfully revoked token: {token[:10]}...")
        except Exception as e:
            logger.error(f"Token revocation error: {str(e)}")
            # Don't raise exception for revocation failures during refresh
            # This ensures the refresh process continues even if revocation fails

    async def revoke_all_user_tokens(self, user_id: str, token_type: Optional[TokenType] = None) -> None:
        """Revoke all tokens for a user."""
        try:
            query = {"sub": user_id}
            if token_type:
                query["type"] = token_type

            result = await self.tokens.update_many(
                query,
                {"$set": {"is_revoked": True}}
            )
            logger.info(
                f"Revoked {result.modified_count} tokens for user {user_id}")
        except Exception as e:
            logger.error(
                f"Token revocation error for user {user_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to revoke tokens"
            )

    async def create_password_reset_token(self, user_id: str) -> str:
        """Create a password reset token."""
        try:
            return await self.create_token(
                user_id=user_id,
                token_type=TokenType.PASSWORD_RESET,
                tenant="system",
                tier="system",
                expires_in=settings.PASSWORD_RESET_TOKEN_EXPIRY
            )
        except Exception as e:
            logger.error(f"Password reset token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create password reset token"
            )

    async def create_invitation_token(
        self,
        user_id: str,
        org_id: str,
        role_id: str
    ) -> str:
        """Create an invitation token."""
        try:
            return await self.create_token(
                user_id=user_id,
                token_type=TokenType.INVITATION,
                tenant=org_id,
                tier="invited",
                expires_in=settings.INVITATION_TOKEN_EXPIRY,
                metadata={
                    "org_id": org_id,
                    "role_id": role_id
                }
            )
        except Exception as e:
            logger.error(f"Invitation token creation error: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create invitation token"
            )

    async def verify_password_reset_token(self, token: str) -> Token:
        """Verify a password reset token."""
        return await self.verify_token(token, TokenType.PASSWORD_RESET)

    async def verify_invitation_token(self, token: str) -> Token:
        """Verify an invitation token."""
        return await self.verify_token(token, TokenType.INVITATION)
