"""
Form submission job handlers.

This module contains handlers for form submission jobs.
"""
from typing import Any, Dict, Optional

from app.core.logging import get_logger
from app.services.factory import get_form_service, get_exclusion_filter_service, get_job_service
from app.services.queue.handlers.base import BaseJobHandler
from app.models.queue import Job
from app.models.job import JobStatus

logger = get_logger(__name__)


class FormSubmissionHandler(BaseJobHandler):
    """Handler for form submission jobs."""

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        self.form_service = None
        self.exclusion_filter_service = None
        self.job_service = None
        self.logger.debug(f"FormSubmissionHandler initialized with services: form={self.form_service}, exclusion={self.exclusion_filter_service}, job={self.job_service}")

    async def _initialize_services(self) -> None:
        """Initialize handler services."""
        self.logger.debug("Starting service initialization in FormSubmissionHandler")
        
        try:
            if self.form_service is None:
                self.logger.debug("Initializing form service")
                self.form_service = await get_form_service()
                await self.form_service.initialize()
                self.logger.debug("Form service initialized successfully")
            
            if self.exclusion_filter_service is None:
                self.logger.debug("Initializing exclusion filter service")
                self.exclusion_filter_service = await get_exclusion_filter_service()
                await self.exclusion_filter_service.initialize()
                self.logger.debug("Exclusion filter service initialized successfully")
            
            if self.job_service is None:
                self.logger.debug("Initializing job service")
                self.job_service = await get_job_service()
                await self.job_service.initialize()
                self.logger.debug("Job service initialized successfully")
                
            self.logger.debug("All services initialized successfully")
        except Exception as e:
            self.logger.error("Failed to initialize services", exc_info=True, extra={
                "form_service": bool(self.form_service),
                "exclusion_filter_service": bool(self.exclusion_filter_service),
                "job_service": bool(self.job_service),
                "error": str(e)
            })
            raise

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a form submission job.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing form submission: {payload.get('submission_id')}")

        # Extract data from payload
        submission_id = payload.get("submission_id")
        form_id = payload.get("form_id")
        org_id = payload.get("org_id")
        answers = payload.get("answers")
        tracked_job_id = payload.get("tracked_job_id")

        self.logger.debug(f"Extracted job data: submission_id={submission_id}, form_id={form_id}, org_id={org_id}, tracked_job_id={tracked_job_id}")

        if not all([submission_id, form_id, org_id]):
            self.logger.error("Missing required fields in payload", extra={"payload": payload})
            raise ValueError("Missing required fields in payload")

        # Update job status if tracked_job_id is provided
        if tracked_job_id:
            try:
                self.logger.debug(f"Updating job status for tracked job {tracked_job_id}")
                await self.job_service.update_job_status(
                    job_id=tracked_job_id,
                    status=JobStatus.IN_PROGRESS,
                    progress=0.2
                )
                self.logger.debug("Job status updated successfully")
            except Exception as e:
                self.logger.error(f"Failed to update job status: {str(e)}", exc_info=True)

        # Check exclusion filters
        if answers:
            try:
                self.logger.info(f"Checking exclusion filters for submission {submission_id}")
                exclusion_check = await self.exclusion_filter_service.check_exclusion(
                    org_id=org_id,
                    form_id=form_id,
                    answers=answers
                )
                self.logger.debug(f"Exclusion check result: {exclusion_check}")

                # If excluded, log and return without processing
                if exclusion_check["excluded"]:
                    self.logger.info(
                        f"Submission {submission_id} excluded by filter: {exclusion_check['filter_name']} - {exclusion_check['reason']}"
                    )

                    # Update job status if tracked_job_id is provided
                    if tracked_job_id:
                        try:
                            await self.job_service.update_job_status(
                                job_id=tracked_job_id,
                                status=JobStatus.COMPLETED,
                                progress=1.0,
                                output={
                                    "excluded": True,
                                    "filter_name": exclusion_check["filter_name"],
                                    "reason": exclusion_check["reason"]
                                }
                            )
                        except Exception as e:
                            self.logger.error(f"Failed to update job status for excluded submission: {str(e)}", exc_info=True)

                    return {
                        "success": True,
                        "excluded": True,
                        "filter_name": exclusion_check["filter_name"],
                        "reason": exclusion_check["reason"]
                    }
            except Exception as e:
                self.logger.error(f"Error checking exclusion filters: {str(e)}", exc_info=True)
                raise

        # Process the submission if not excluded
        try:
            # Update job status to indicate progress
            if tracked_job_id:
                try:
                    self.logger.debug(f"Updating job status to in progress for {tracked_job_id}")
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id,
                        status=JobStatus.IN_PROGRESS,
                        progress=0.5
                    )
                except Exception as e:
                    self.logger.error(f"Failed to update job status: {str(e)}", exc_info=True)

            self.logger.debug(f"Processing submission {submission_id} with form service")
            result = await self.form_service.process_submission(
                org_id=org_id,
                form_id=form_id,
                submission_id=submission_id
            )
            self.logger.debug(f"Form service processing result: {result}")

            # Update job status to completed
            if tracked_job_id:
                try:
                    self.logger.debug(f"Updating job status to completed for {tracked_job_id}")
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id,
                        status=JobStatus.COMPLETED,
                        progress=1.0,
                        output={"result": result}
                    )
                except Exception as e:
                    self.logger.error(f"Failed to update final job status: {str(e)}", exc_info=True)

            self.logger.info(f"Processed form submission {submission_id} successfully")
            return {"success": True, "result": result}
        except Exception as e:
            self.logger.error(f"Error processing form submission: {str(e)}", exc_info=True, extra={
                "submission_id": submission_id,
                "form_id": form_id,
                "org_id": org_id
            })
            # Update job status to failed if there's an error
            if tracked_job_id:
                try:
                    await self.job_service.update_job_status(
                        job_id=tracked_job_id,
                        status=JobStatus.FAILED,
                        error=str(e)
                    )
                except Exception as job_error:
                    self.logger.error(f"Failed to update job status for failed submission: {str(job_error)}", exc_info=True)
            raise


def create_form_submission_handler() -> FormSubmissionHandler:
    """Create a new form submission handler instance."""
    logger.debug("Creating new FormSubmissionHandler instance")
    return FormSubmissionHandler()

# Register handlers
HANDLERS = {
    "process_form_submission": create_form_submission_handler
}
