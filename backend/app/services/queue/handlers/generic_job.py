"""
Generic job handler.

This module contains a generic handler for tracked jobs.
"""
from typing import Any, Dict, Optional, Callable, Awaitable, Union
import importlib
import inspect

from app.core.logging import get_logger
from app.services.job.mongo import JobService
from app.services.queue.handlers.base import BaseJobHandler
from app.models.queue import Job
from app.models.job import JobStatus

logger = get_logger(__name__)


class GenericJobHandler(BaseJobHandler):
    """Generic handler for tracked jobs."""

    def __init__(self):
        """Initialize the handler."""
        super().__init__()
        self.job_service = JobService()

    async def process(self, job: Job) -> Optional[Dict[str, Any]]:
        """
        Process a generic job.

        This handler can process any type of job by dynamically loading
        and executing the processor function specified in the job payload.

        Args:
            job: The job to process

        Returns:
            Processing result
        """
        payload = job.payload
        self.logger.info(f"Processing generic job: {job.id}")

        # Extract data from payload
        tracked_job_id = payload.get("tracked_job_id")
        if not tracked_job_id:
            self.logger.error("Missing tracked_job_id in payload")
            raise ValueError("Missing tracked_job_id in payload")

        # Initialize job service
        await self.job_service.initialize()

        # Update job status to in progress
        await self.job_service.update_job_status(
            job_id=tracked_job_id,
            status=JobStatus.IN_PROGRESS,
            progress=0.1
        )

        try:
            # Extract processor information
            processor_info = payload.get("processor")
            if not processor_info:
                raise ValueError("Missing processor information in payload")

            # Extract processor module and function
            module_path = processor_info.get("module")
            function_name = processor_info.get("function")
            args = processor_info.get("args", [])
            kwargs = processor_info.get("kwargs", {})

            if not module_path or not function_name:
                raise ValueError("Invalid processor information: module and function are required")

            # Load the processor module and function
            processor_module = importlib.import_module(module_path)
            processor_function = getattr(processor_module, function_name)

            # Check if the function is async
            is_async = inspect.iscoroutinefunction(processor_function)

            # Add tracked_job_id to kwargs if the function accepts it
            sig = inspect.signature(processor_function)
            if "tracked_job_id" in sig.parameters:
                kwargs["tracked_job_id"] = tracked_job_id

            # Add job_service to kwargs if the function accepts it
            if "job_service" in sig.parameters:
                kwargs["job_service"] = self.job_service

            # Execute the processor function
            if is_async:
                result = await processor_function(*args, **kwargs)
            else:
                result = processor_function(*args, **kwargs)

            # Update job status to completed
            await self.job_service.update_job_status(
                job_id=tracked_job_id,
                status=JobStatus.COMPLETED,
                progress=1.0,
                output={"result": result}
            )

            self.logger.info(f"Completed generic job {tracked_job_id} successfully")
            return {"success": True, "result": result}

        except Exception as e:
            self.logger.error(f"Error processing generic job: {str(e)}", exc_info=True)
            
            # Update job status to failed
            await self.job_service.update_job_status(
                job_id=tracked_job_id,
                status=JobStatus.FAILED,
                error=str(e)
            )
            
            raise


async def process_generic_job(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a generic job.

    Args:
        payload: Job payload containing job data

    Returns:
        Processing result
    """
    handler = GenericJobHandler()
    job = Job(id="generic", type="generic_job", payload=payload)

    try:
        result = await handler.process(job)
        return result or {"success": True}
    except Exception as e:
        logger.error(f"Error processing generic job: {str(e)}")
        return {"success": False, "error": str(e)}


# Function to create a processor for a specific job type
def create_job_processor(
    processor_func: Union[Callable[..., Any], Callable[..., Awaitable[Any]]],
    module_path: Optional[str] = None
) -> Callable[[Dict[str, Any]], Dict[str, Any]]:
    """
    Create a job processor for a specific job type.

    Args:
        processor_func: Function to process the job
        module_path: Module path of the processor function (defaults to the function's module)

    Returns:
        Job processor function
    """
    if module_path is None:
        module_path = processor_func.__module__

    async def processor(payload: Dict[str, Any]) -> Dict[str, Any]:
        # Add processor information to the payload
        payload.setdefault("processor", {}).update({
            "module": module_path,
            "function": processor_func.__name__
        })
        
        # Process the job
        return await process_generic_job(payload)

    return processor


# Register handlers
HANDLERS = {
    "generic_job": process_generic_job
}
