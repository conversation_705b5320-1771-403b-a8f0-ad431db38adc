"""
Queue handlers for qualifier form operations.
"""

from typing import Any, Dict

from app.core.logging import get_logger
from app.services.factory import get_trigger_service

logger = get_logger(__name__)


async def process_resource_event(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Process a resource event and execute matching triggers.
    
    Args:
        payload: Job payload containing event details
        
    Returns:
        Result of the event processing
    """
    resource_type = payload.get("resource_type")
    resource_id = payload.get("resource_id")
    event_type = payload.get("event_type")
    event_data = payload.get("event_data", {})
    
    logger.info(f"Processing {event_type} event for {resource_type} {resource_id}")
    
    # Get trigger service
    trigger_service = await get_trigger_service()
    
    # Process event
    executions = await trigger_service.process_resource_event(
        resource_type=resource_type,
        resource_id=resource_id,
        event_type=event_type,
        event_data=event_data
    )
    
    return {
        "success": True,
        "resource_type": resource_type,
        "resource_id": resource_id,
        "event_type": event_type,
        "executions": len(executions)
    }


async def execute_trigger(payload: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute a trigger.
    
    Args:
        payload: Job payload containing trigger details
        
    Returns:
        Result of the trigger execution
    """
    execution_id = payload.get("execution_id")
    trigger_id = payload.get("trigger_id")
    trigger_type = payload.get("trigger_type")
    event_data = payload.get("event_data", {})
    config = payload.get("config", {})
    
    logger.info(f"Executing trigger {trigger_id} of type {trigger_type}")
    
    # Get trigger service
    trigger_service = await get_trigger_service()
    
    # Get processor for this trigger type
    processor = trigger_service.processors.get(trigger_type)
    if not processor:
        error = f"No processor found for trigger type: {trigger_type}"
        logger.error(error)
        
        # Update execution status
        await trigger_service.db.trigger_executions.update_one(
            {"_id": execution_id},
            {"$set": {
                "status": "failed",
                "error": error,
                "updated_at": trigger_service._timestamp_ms()
            }}
        )
        
        return {
            "success": False,
            "error": error
        }
    
    try:
        # Execute the processor
        start_time = trigger_service._timestamp_ms()
        result = await processor(config, event_data)
        end_time = trigger_service._timestamp_ms()
        execution_time = end_time - start_time
        
        # Update execution status
        await trigger_service.db.trigger_executions.update_one(
            {"_id": execution_id},
            {"$set": {
                "status": "success" if result.get("success", False) else "failed",
                "result": result,
                "error": result.get("error"),
                "execution_time": execution_time,
                "updated_at": end_time
            }}
        )
        
        return {
            "success": True,
            "execution_id": execution_id,
            "trigger_id": trigger_id,
            "execution_time": execution_time,
            "result": result
        }
    except Exception as e:
        error = f"Error executing trigger: {str(e)}"
        logger.error(error)
        
        # Update execution status
        await trigger_service.db.trigger_executions.update_one(
            {"_id": execution_id},
            {"$set": {
                "status": "failed",
                "error": error,
                "updated_at": trigger_service._timestamp_ms()
            }}
        )
        
        return {
            "success": False,
            "error": error
        }


# Map of job types to handler functions
HANDLERS = {
    "process_resource_event": process_resource_event,
    "execute_trigger": execute_trigger
}
