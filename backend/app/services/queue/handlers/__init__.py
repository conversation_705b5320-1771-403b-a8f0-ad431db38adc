"""
Queue job handlers.

This package contains handlers for queue jobs.
"""
from typing import Dict, Callable, Union  # noqa: F401

from app.services.queue.worker_interface import JobHandlerInterface

# Import handlers
from app.services.queue.handlers.form_submission import HANDLERS as FORM_SUBMISSION_HANDLERS
from app.services.queue.handlers.qualifier_form import process_resource_event
from app.services.queue.handlers.job_tracking import HANDLERS as JOB_TRACKING_HANDLERS
from app.services.queue.handlers.generic_job import HANDLERS as GENERIC_JOB_HANDLERS

# Create handler registry
HANDLERS: Dict[str, Union[JobHandlerInterface, Callable]] = {
    # Function-based handlers
    "process_resource_event": process_resource_event,
}

# Add form submission handlers
HANDLERS.update(FORM_SUBMISSION_HANDLERS)

# Add job tracking handlers
HANDLERS.update(JOB_TRACKING_HANDLERS)

# Add generic job handlers
HANDLERS.update(GENERIC_JOB_HANDLERS)

__all__ = ["HANDLERS"]
