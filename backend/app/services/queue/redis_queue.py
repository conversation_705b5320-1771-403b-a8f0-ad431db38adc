"""
Redis queue service implementation.

This module provides a Redis-specific implementation of the queue service interface.
It handles Redis-specific initialization and configuration while delegating queue operations to the backend.
"""
import uuid
import time
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from redis.asyncio.client import Redis # type: ignore

from app.core.config import settings
from app.services.base import BaseService
from app.services.queue.backends.redis_backend import RedisQueueBackend
from app.services.queue.interfaces import (
    QueueBackendInterface,
    QueueServiceInterface,
    QueueType,
    JobStatus,
    JobPriority
)
from app.models.queue import Job, JobMetadata
from app.core.logging import get_logger

logger = get_logger(__name__)


class RedisQueueService(BaseService, QueueServiceInterface):
    """Redis implementation of the queue service."""

    def __init__(self):
        """Initialize the Redis queue service."""
        super().__init__()
        self.backend: Optional[QueueBackendInterface] = None
        self.redis: Optional[Redis] = None

    async def initialize(self) -> None:
        """Initialize Redis connection and backend."""
        if not self.backend:
            logger.info("Initializing Redis backend for queue service")
            self.backend = RedisQueueBackend(redis_url=settings.REDIS_URL)
            await self.backend.initialize()
            self.redis = self.backend.redis_client
            
    async def get_backend(self) -> QueueBackendInterface:
        """Get the queue backend implementation."""
        if not self.backend:
            await self.initialize()
        return self.backend

    async def cleanup(self) -> None:
        """Cleanup Redis connection and backend."""
        if self.backend:
            await self.backend.cleanup()
            self.backend = None
            self.redis = None
            logger.info("Disconnected from Redis queue service")

    def _build_queue_key(self, queue_type: QueueType) -> str:
        """Build a key for a queue."""
        return f"{self.prefix}:{queue_type.value}"

    def cc(self, job_id: str) -> str:
        """Build a key for a job."""
        return f"{self.prefix}:job:{job_id}"

    def _build_scheduled_key(self) -> str:
        """Build a key for the scheduled jobs sorted set."""
        return f"{self.prefix}:scheduled"

    def _build_processing_key(self, queue_type: QueueType) -> str:
        """Build a key for jobs being processed."""
        return f"{self.prefix}:processing:{queue_type.value}"

    def _build_stats_key(self, queue_type: QueueType) -> str:
        """Build a key for queue statistics."""
        return f"{self.prefix}:stats:{queue_type.value}"

    def _timestamp_ms(self) -> int:
        """Get current timestamp in milliseconds."""
        return int(time.time() * 1000)

    def _datetime_to_timestamp(self, dt: datetime) -> int:
        """Convert datetime to unix timestamp."""
        return int(dt.timestamp())

    async def enqueue_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        queue_type: QueueType = QueueType.DEFAULT,
        priority: JobPriority = JobPriority.NORMAL,
        job_id: Optional[str] = None,
        delay_seconds: int = 0,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Add a job to the queue."""
        try:
            # Get backend
            backend = await self.get_backend()
            
            # Create job ID if not provided
            if not job_id:
                job_id = str(uuid.uuid4())
            
            # Create job metadata
            job_metadata = JobMetadata(**(metadata or {}))
            
            # Create job
            job = Job(
                id=job_id,
                type=job_type,
                payload=payload,
                queue=queue_type,
                priority=priority,
                metadata=job_metadata
            )
            
            # Apply retry configuration if provided
            if retry_config:
                for key, value in retry_config.items():
                    if hasattr(job, key):
                        setattr(job, key, value)
            
            # Handle delay
            if delay_seconds > 0:
                job.scheduled_for = int(time.time() * 1000) + (delay_seconds * 1000)
            
            # Enqueue job using backend
            enqueued_job = await backend.enqueue(job)
            
            # Log success
            if delay_seconds > 0:
                logger.info(f"Scheduled job {job_id} for {delay_seconds} seconds from now")
            else:
                logger.info(f"Enqueued job {job_id} to {queue_type.value} queue with {priority.value} priority")
            
            return enqueued_job.to_dict()
            
        except Exception as e:
            await self.handle_error(e, {
                "job_type": job_type,
                "queue_type": queue_type.value,
                "error": "Failed to enqueue job"
            })
            raise

    async def dequeue_job(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        job_types: Optional[List[str]] = None,
        wait_timeout: int = 0
    ) -> Optional[Dict[str, Any]]:
        """Get a job from the queue for processing."""
        try:
            backend = await self.get_backend()
            job = await backend.dequeue(queue_type, job_types, wait_timeout)
            if job:
                logger.info(f"Dequeued job {job.id} from {queue_type.value} queue")
                return job.to_dict()
            return None
        except Exception as e:
            await self.handle_error(e, {
                "queue_type": queue_type.value,
                "error": "Failed to dequeue job"
            })
            return None

    async def complete_job(
        self,
        job_id: str,
        result: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Mark a job as completed."""
        try:
            backend = await self.get_backend()
            success = await backend.complete(job_id, result)
            if success:
                logger.info(f"Completed job {job_id}")
            else:
                logger.warning(f"Job {job_id} not found for completion")
            return success
        except Exception as e:
            await self.handle_error(e, {
                "job_id": job_id,
                "error": "Failed to complete job"
            })
            return False

    async def fail_job(
        self,
        job_id: str,
        error: str,
        retry: bool = True
    ) -> bool:
        """Mark a job as failed."""
        try:
            backend = await self.get_backend()
            success = await backend.fail(job_id, error, retry)
            if success:
                logger.info(f"Failed job {job_id}: {error}" + (" (with retry)" if retry else " (no retry)"))
            else:
                logger.warning(f"Job {job_id} not found for failure marking")
            return success
        except Exception as e:
            await self.handle_error(e, {
                "job_id": job_id,
                "error": "Failed to mark job as failed"
            })
            return False


    async def _increment_stat(self, queue_type: QueueType, stat_name: str) -> None:
        """Increment a statistic counter for a queue."""
        stats_key = self._build_stats_key(queue_type)
        await self.redis.hincrby(stats_key, stat_name, 1)

    async def schedule_job(
        self,
        job_type: str,
        payload: Dict[str, Any],
        scheduled_time: Union[datetime, int],
        job_id: Optional[str] = None,
        retry_config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Schedule a job to run at a specific time."""
        try:
            # Get backend
            backend = await self.get_backend()
            
            # Create job ID if not provided
            if not job_id:
                job_id = str(uuid.uuid4())
            
            # Create job metadata
            job_metadata = JobMetadata(**(metadata or {}))
            
            # Convert scheduled time to timestamp if needed
            if isinstance(scheduled_time, datetime):
                scheduled_for = int(scheduled_time.timestamp() * 1000)
            else:
                scheduled_for = scheduled_time
            
            # Create job
            job = Job(
                id=job_id,
                type=job_type,
                payload=payload,
                queue=QueueType.SCHEDULED,
                scheduled_for=scheduled_for,
                metadata=job_metadata
            )
            
            # Apply retry configuration if provided
            if retry_config:
                for key, value in retry_config.items():
                    if hasattr(job, key):
                        setattr(job, key, value)
            
            # Enqueue job using backend
            enqueued_job = await backend.enqueue(job)
            
            # Log success
            logger.info(f"Scheduled job {job_id} for {datetime.fromtimestamp(scheduled_for/1000)}")
            
            return enqueued_job.to_dict()
            
        except Exception as e:
            await self.handle_error(e, {
                "job_type": job_type,
                "error": "Failed to schedule job"
            })
            raise

    # Delegate all other operations to the backend
    async def get_job(self, job_id: str) -> Optional[Dict[str, Any]]:
        """Get job details by ID."""
        backend = await self.get_backend()
        job = await backend.get_job(job_id)
        return job.to_dict() if job else None

    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a pending or scheduled job."""
        backend = await self.get_backend()
        return await backend.cancel(job_id)

    async def retry_job(self, job_id: str, delay_seconds: int = 0) -> bool:
        """Retry a failed job."""
        backend = await self.get_backend()
        return await backend.retry(job_id, delay_seconds)

    async def purge_queue(
        self,
        queue_type: QueueType,
        older_than: Optional[Union[datetime, int]] = None
    ) -> int:
        """Remove all jobs from a queue."""
        backend = await self.get_backend()
        return await backend.purge(queue_type, older_than)

    async def list_jobs(
        self,
        queue_type: QueueType = QueueType.DEFAULT,
        status: Optional[JobStatus] = None,
        job_type: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """List jobs in a queue."""
        backend = await self.get_backend()
        jobs = await backend.list_jobs(queue_type, status, job_type, limit, offset)
        return [job.to_dict() for job in jobs]

    async def get_queue_stats(
        self,
        queue_type: Optional[QueueType] = None
    ) -> Dict[str, Any]:
        """Get statistics about the queue(s)."""
        backend = await self.get_backend()
        stats = await backend.get_stats(queue_type)
        if isinstance(stats, dict):
            return {k: v.dict() for k, v in stats.items()}
        return stats.dict()

    async def process_scheduled_jobs(self) -> int:
        """Process scheduled jobs that are due."""
        backend = await self.get_backend()
        return await backend.process_scheduled_jobs()

    async def process_stalled_jobs(self) -> int:
        """Process stalled jobs (jobs that have been processing for too long)."""
        backend = await self.get_backend()
        return await backend.process_stalled_jobs()
