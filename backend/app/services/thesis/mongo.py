"""
MongoDB Implementation of Investment Thesis Service

This module implements the ThesisServiceInterface using MongoDB as the data store.
"""

from typing import List, Optional, Dict, Any, Union, Callable

from bson import ObjectId
from app.models.thesis import (
    InvestmentThesis, ThesisWithRules, ScoringRule, MatchRule,
    ThesisStatus, AggregationType, RuleType, FilterCondition, CompoundCondition, LogicalOperator
)

from app.core.logging import get_logger
from motor.motor_asyncio import AsyncIOMotorDatabase
from app.services.base import BaseService
from app.services.thesis.interfaces import ThesisServiceInterface
from app.services.factory import get_form_service
from app.utils.scoring.scoring import evaluate_condition
from app.utils.thesis.helpers import (
    validate_thesis_ownership, validate_rule_ownership,
    create_scoring_rules, create_match_rules)

from app.utils.model.type_casting import update_model_with_type_casting

logger = get_logger(__name__)


class ThesisService(BaseService, ThesisServiceInterface):
    """MongoDB implementation of ThesisService."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__()
        self.form_service: Optional[ThesisServiceInterface] = None

    async def initialize(self) -> None:
        """Initialize database connection and services."""
        self.form_service = await get_form_service()

    async def cleanup(self) -> None:
        """Cleanup database connection."""
        pass

    async def validate_thesis_ownership(
        self,
        thesis_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId]
    ) -> InvestmentThesis:
        """
        Validate that a thesis exists and belongs to the organization.

        Args:
            thesis_id: ID of the thesis to validate
            org_id: Organization ID to check ownership against

        Returns:
            The thesis if it exists and belongs to the organization

        Raises:
            ValueError: If thesis doesn't exist or doesn't belong to the organization
        """
        return await validate_thesis_ownership(thesis_id, org_id, self.get_thesis)

    async def validate_rule_ownership(
        self,
        rule_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        get_rule_func: Callable,
        rule_type: str = "rule"
    ) -> Union[ScoringRule, MatchRule]:
        """
        Validate that a rule exists and belongs to an organization's thesis.

        Args:
            rule_id: ID of the rule to validate
            org_id: Organization ID to check ownership against
            get_rule_func: Function to get the rule (get_scoring_rule or get_match_rule)
            rule_type: Type of rule for error messages

        Returns:
            The rule if it exists and belongs to the organization's thesis

        Raises:
            ValueError: If rule doesn't exist or doesn't belong to the organization's thesis
        """
        return await validate_rule_ownership(
            rule_id, org_id, get_rule_func, self.get_thesis, rule_type
        )

    async def create_thesis_scoring_rules(
        self,
        thesis_id: Union[str, ObjectId],
        rules_data: List[Dict[str, Any]]
    ) -> List[ScoringRule]:
        """
        Create scoring rules for a thesis.

        Args:
            thesis_id: ID of the thesis to create rules for
            rules_data: List of rule data dictionaries

        Returns:
            List of created scoring rules
        """

        return await create_scoring_rules(thesis_id, rules_data, self.create_scoring_rule)

    async def create_thesis_match_rules(
        self,
        thesis_id: Union[str, ObjectId],
        rules_data: List[Dict[str, Any]]
    ) -> List[MatchRule]:
        """
        Create match rules for a thesis.

        Args:
            thesis_id: ID of the thesis to create rules for
            rules_data: List of rule data dictionaries

        Returns:
            List of created match rules
        """
        return await create_match_rules(thesis_id, rules_data, self.create_match_rule)

    async def update_thesis_scoring_rules(
        self,
        thesis_id: Union[str, ObjectId],
        scoring_rules: List[Dict[str, Any]]
    ) -> None:
        """
        Update scoring rules for a thesis.

        Args:
            thesis_id: ID of the thesis to update rules for
            scoring_rules: List of rule data dictionaries
        """
        existing_rules = await self.list_scoring_rules(thesis_id)
        logger.info(f"Existing scoring rules: {existing_rules}")
        existing_rule_map = {str(rule.id): rule for rule in existing_rules}
        
        # Create a map of existing scoring rules by question_id for duplicate checking
        existing_scoring_by_question = {}
        for rule in existing_rules:
            if rule.rule_type == RuleType.SCORING and rule.question_id:
                existing_scoring_by_question[str(rule.question_id)] = rule
        
        for rule_data in scoring_rules:
            rule_id = rule_data.get("_id", rule_data.get("id"))
            if rule_id and rule_id in existing_rule_map:
                logger.info(f"Updating scoring rule {rule_id} with data: {rule_data}")
                await self.update_scoring_rule(rule_id, rule_data)
            elif rule_data:
                logger.info(f"Creating scoring rule with data: {rule_data}, Thesis ID: {thesis_id}")
                data = rule_data.copy()
                if "thesis_id" in data:
                    del data["thesis_id"]
                
                # Check for duplicate question_id if rule_type is scoring
                rule_type = data.get("rule_type", RuleType.SCORING)
                question_id = data.get("question_id")
                
                if rule_type == RuleType.SCORING and question_id and str(question_id) in existing_scoring_by_question:
                    # Update existing rule instead of creating new one
                    existing_rule = existing_scoring_by_question[str(question_id)]
                    logger.info(f"Found existing scoring rule for question_id {question_id}, updating rule {existing_rule.id}")
                    await self.update_scoring_rule(existing_rule.id, data)
                else:
                    # Create new rule
                    await self.create_scoring_rule(thesis_id, **data)
            else:
                logger.warning(f"Skipping rule with missing ID or name: {rule_data}")

    async def update_thesis_match_rules(
        self,
        thesis_id: Union[str, ObjectId],
        match_rules: List[Dict[str, Any]]
    ) -> None:
        """
        Update match rules for a thesis.

        Args:
            thesis_id: ID of the thesis to update rules for
            match_rules: List of rule data dictionaries
        """
        existing_rules = await self.list_match_rules(thesis_id)
        existing_rule_map = {str(rule.id): rule for rule in existing_rules}
        for rule_data in match_rules:
            rule_id = rule_data.get("_id", rule_data.get("id"))
            if rule_id and rule_id in existing_rule_map:
                logger.info(f"Updating match rule {rule_id} with data: {rule_data}")
                await self.update_match_rule(rule_id, rule_data)
            elif rule_data.get("name"):
                data = rule_data.copy()
                if "thesis_id" in data:
                    del data["thesis_id"]
                await self.create_match_rule(thesis_id, **data)
            else:
                logger.warning(f"Skipping rule with missing ID or name: {rule_data}")

    async def get_thesis(self, thesis_id: Union[str, ObjectId]) -> Optional[InvestmentThesis]:
        """Get thesis by ID."""
        try:
            return await InvestmentThesis.get_by_id(thesis_id)
        except Exception as e:
            await self.handle_error(e, {"thesis_id": str(thesis_id)})
            return None

    async def get_thesis_with_rules(self, thesis_id: Union[str, ObjectId]) -> Optional[ThesisWithRules]:
        """Get thesis by ID with all rules expanded."""
        try:
            # Convert string ID to ObjectId if needed
            if isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)
            
            # Get thesis
            thesis = await InvestmentThesis.find_one({'_id': ObjectId(thesis_id)})
            if not thesis:
                logger.info(f"Thesis not found with ID: {thesis_id}")
                return None

            logger.info(f"Found thesis: {thesis.name}")

            # Get scoring rules directly to avoid None values from populate
            scoring_rules = await ScoringRule.find_many(
                query={"thesis_id": thesis_id, "is_deleted": False},
                sort=[("created_at", 1)]
            )
            logger.info(f"Found {len(scoring_rules)} scoring rules")

            # Get match rules directly to avoid None values from populate
            match_rules = await MatchRule.find_many(
                query={"thesis_id": thesis_id, "is_deleted": False},
                sort=[("created_at", 1)]
            )
            logger.info(f"Found {len(match_rules)} match rules")

            # Create ThesisWithRules with actual rule objects
            thesis_data = thesis.model_dump(by_alias=True)

            # Replace the rule ID lists with the actual rule objects
            thesis_data["scoring_rules"] = scoring_rules
            thesis_data["match_rules"] = match_rules

            # Create and return the ThesisWithRules object
            result = ThesisWithRules(**thesis_data)
            logger.info(f"Created ThesisWithRules with {len(result.scoring_rules)} scoring rules and {len(result.match_rules)} match rules")
            return result
        except Exception as e:
            # Log the specific error for debugging
            import traceback
            error_detail = f"Error in get_thesis_with_rules: {str(e)}\n{traceback.format_exc()}"
            await self.handle_error(Exception(error_detail), {"thesis_id": str(thesis_id)})
            return None

    async def create_thesis(
        self,
        name: str,
        description: str,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        status: ThesisStatus = ThesisStatus.DRAFT,
        is_active: bool = True
    ) -> InvestmentThesis:
        """Create a new investment thesis."""
        try:
            # Convert string IDs to ObjectId
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Create thesis
            thesis = InvestmentThesis(
                name=name,
                description=description,
                form_id=form_id,
                org_id=org_id,
                created_by=created_by,
                status=status,
                is_active=is_active
            )

            # Insert thesis
            await thesis.save()
            thesis_with_rules = await self.get_thesis_with_rules(thesis.id)
            return thesis_with_rules
        except Exception as e:
            await self.handle_error(e, {
                "name": name,
                "description": description,
                "form_id": str(form_id),
                "org_id": str(org_id),
                "created_by": str(created_by)
            })
            raise

    async def update_thesis(
        self,
        thesis_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[InvestmentThesis]:
        """Update an existing thesis. Excludes scoring_rules and match_rules (updated separately)."""
        try:
            if isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)

            # Check if thesis exists
            thesis = await self.get_thesis(thesis_id)
            if not thesis:
                return None

            # Remove scoring_rules and match_rules from update_data (they are updated separately)
            update_data = {k: v for k, v in update_data.items() if k not in ("scoring_rules", "match_rules")}

            if update_data:
                # Use the type casting utility to update the thesis in-place
                thesis = update_model_with_type_casting(thesis, update_data)

            await thesis.save(is_update=True)
            return thesis
        except Exception as e:
            await self.handle_error(e, {"thesis_id": str(thesis_id), "update_data": update_data})
            return None

    async def delete_thesis(self, thesis_id: Union[str, ObjectId]) -> bool:
        """Delete a thesis and all its rules."""
        try:
            if isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)

            # Get thesis
            thesis = await self.get_thesis(thesis_id)
            if not thesis:
                return False

            # Delete scoring rules
            for rule_id in thesis.scoring_rules:
                try:
                    # Use the delete_by_id method to ensure proper deletion
                    await ScoringRule.delete_by_id(rule_id)
                except Exception as rule_error:
                    # Log error but continue with other rules
                    await self.handle_error(rule_error, {"rule_id": str(rule_id)})

            # Delete match rules
            for rule_id in thesis.match_rules:
                try:
                    # Use the delete_by_id method to ensure proper deletion
                    await MatchRule.delete_by_id(rule_id)
                except Exception as rule_error:
                    # Log error but continue with other rules
                    await self.handle_error(rule_error, {"rule_id": str(rule_id)})

            # Delete thesis
            await thesis.delete()

            # Double-check for any orphaned rules and delete them
            orphaned_scoring_rules = await ScoringRule.find_many({"thesis_id": thesis_id})
            for rule in orphaned_scoring_rules:
                await rule.delete()

            orphaned_match_rules = await MatchRule.find_many({"thesis_id": thesis_id})
            for rule in orphaned_match_rules:
                await rule.delete()

            return True
        except Exception as e:
            await self.handle_error(e, {"thesis_id": str(thesis_id)})
            return False

    async def list_theses(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        form_id: Optional[Union[str, ObjectId]] = None,
        status: Optional[ThesisStatus] = None,
        is_active: Optional[bool] = None
    ) -> List[InvestmentThesis]:
        """List theses with optional filtering."""
        try:
            # Build query
            query = {"org_id": ObjectId(org_id)}
            if form_id:
                query["form_id"] = ObjectId(form_id)
            if status:
                query["status"] = status
            if is_active is not None:
                query["is_active"] = is_active

            # Execute query using find_many
            theses = await InvestmentThesis.find_many(
                query=query,
                skip=skip,
                limit=limit,
                sort=[("created_at", -1)]
            )

            return theses
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "skip": skip,
                "limit": limit,
                "form_id": str(form_id) if form_id else None,
                "status": status.value if status else None,
                "is_active": is_active
            })
            return []

    async def create_scoring_rule(
        self,
        thesis_id: Union[str, ObjectId],
        **kwargs
    ) -> ScoringRule:
        """Create a new scoring rule for a thesis."""
        try:
            # Validate thesis exists
            thesis = await self.get_thesis(thesis_id)
            if not thesis:
                raise ValueError(f"Thesis not found with ID: {thesis_id}")

            # Validate rule type
            rule_type = kwargs.get("rule_type", RuleType.SCORING)
            if rule_type not in [rt.value for rt in RuleType]:
                raise ValueError(f"Invalid rule type: {rule_type}")

            # Validate rule-specific fields
            if rule_type == RuleType.SCORING:
                if not kwargs.get("question_id"):
                    raise ValueError("question_id is required for scoring rules")
                if kwargs.get("bonus_points") is not None:
                    raise ValueError("bonus_points cannot be set for scoring rules")
                if not kwargs.get("weight") or kwargs.get("weight") <= 0:
                    raise ValueError("positive weight is required for scoring rules")
            else:  # BONUS rule
                if not kwargs.get("bonus_points"):
                    raise ValueError("bonus_points is required for bonus rules")
                if kwargs.get("weight") != 1.0:
                    kwargs["weight"] = 1.0  # Force weight to 1.0 for bonus rules

            # Validate condition
            condition = kwargs.get("condition")
            if not condition:
                raise ValueError("condition is required for all rules")

            # Validate aggregation fields if present
            if kwargs.get("aggregation") is not None:
                if not kwargs.get("section_id"):
                    raise ValueError("section_id is required when using aggregation")
                if kwargs.get("aggregation") != AggregationType.NONE.value:
                    if not kwargs.get("value_field"):
                        raise ValueError("value_field is required for non-NONE aggregation")

            # Check for duplicate question_id if rule_type is scoring
            if rule_type == RuleType.SCORING and kwargs.get("question_id"):
                existing_rule = await ScoringRule.find_one({
                    "thesis_id": ObjectId(thesis_id),
                    "question_id": ObjectId(kwargs["question_id"]),
                    "rule_type": RuleType.SCORING,
                    "is_deleted": False
                })
                
                if existing_rule:
                    raise ValueError(f"A scoring rule already exists for question_id {kwargs['question_id']}. Use update instead of create.")

            # Create rule
            rule = ScoringRule(
                thesis_id=thesis_id,
                **kwargs
            )

            # Insert rule
            await rule.save()

            # Update thesis to include this rule
            thesis.scoring_rules.append(rule.id)
            await thesis.save(is_update=True)

            return rule
        except Exception as e:
            await self.handle_error(e, {
                "thesis_id": str(thesis_id),
                **kwargs
            })
            raise

    async def update_scoring_rule(
        self,
        rule_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[ScoringRule]:
        """Update an existing scoring rule."""
        try:
            if isinstance(rule_id, str):
                rule_id = ObjectId(rule_id)

            # Get existing rule
            rule = await ScoringRule.find_one({"_id": ObjectId(rule_id)})
            if not rule:
                return None

            # Validate rule type changes
            if "rule_type" in update_data and update_data["rule_type"] != rule.rule_type:
                raise ValueError("Cannot change rule_type of existing rule")

            # Validate rule-specific fields
            rule_type = update_data.get("rule_type", rule.rule_type)
            if rule_type == RuleType.SCORING:
                if "bonus_points" in update_data and update_data["bonus_points"] is not None:
                    raise ValueError("bonus_points cannot be set for scoring rules")
                if "weight" in update_data and update_data["weight"] <= 0:
                    raise ValueError("weight must be positive for scoring rules")
            else:  # BONUS rule
                if "weight" in update_data and update_data["weight"] != 1.0:
                    update_data["weight"] = 1.0  # Force weight to 1.0 for bonus rules
                if "bonus_points" in update_data and not update_data["bonus_points"]:
                    raise ValueError("bonus_points is required for bonus rules")

            # Validate condition if being updated
            if "condition" in update_data and not update_data["condition"]:
                raise ValueError("condition is required for all rules")

            # Validate aggregation fields if being updated
            if "aggregation" in update_data and update_data["aggregation"] is not None:
                if update_data["aggregation"] != AggregationType.NONE.value:
                    if not update_data.get("section_id"):
                        raise ValueError("section_id is required when using aggregation")
                    if not update_data.get("value_field"):
                        raise ValueError("value_field is required for non-NONE aggregation")

            # Check for duplicate question_id if updating scoring rule
            if rule_type == RuleType.SCORING and "question_id" in update_data:
                existing_rule = await ScoringRule.find_one({
                    "thesis_id": rule.thesis_id,
                    "question_id": ObjectId(update_data["question_id"]),
                    "rule_type": RuleType.SCORING,
                    "is_deleted": False,
                    "_id": {"$ne": rule_id}  # Exclude current rule
                })
                
                if existing_rule:
                    raise ValueError(f"Another scoring rule already exists for question_id {update_data['question_id']}")

            # Update rule using type-casting
            rule = update_model_with_type_casting(rule, update_data)
            await rule.save(is_update=True)
            
            

            return rule
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id), "update_data": update_data})
            raise

    async def delete_scoring_rule(self, rule_id: Union[str, ObjectId]) -> bool:
        """Delete a scoring rule."""
        try:
            if isinstance(rule_id, str):
                rule_id = ObjectId(rule_id)

            # Get rule
            rule = await self.get_scoring_rule(rule_id)
            if not rule:
                return False

            # Remove rule from thesis
            thesis = await self.get_thesis(rule.thesis_id)
            if thesis and rule.id in thesis.scoring_rules:
                thesis.scoring_rules.remove(rule.id)
                await thesis.save(is_update=True)

            # Delete rule using the delete method
            await rule.delete()

            return True
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id)})
            return False

    async def get_scoring_rule(self, rule_id: Union[str, ObjectId]) -> Optional[ScoringRule]:
        """Get a scoring rule by ID."""
        try:
            return await ScoringRule.get_by_id(rule_id)
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id)})
            return None

    async def list_scoring_rules(
        self,
        thesis_id: Union[str, ObjectId],
        exclude_from_scoring: Optional[bool] = None,
        rule_type: Optional[str] = None
    ) -> List[ScoringRule]:
        """List scoring rules for a thesis with optional filtering."""
        try:
            # Build query
            query = {"thesis_id": ObjectId(thesis_id)}
            if exclude_from_scoring is not None:
                query["exclude_from_scoring"] = exclude_from_scoring
            if rule_type:
                query["rule_type"] = rule_type

            # Execute query using find_many
            rules = await ScoringRule.find_many(
                query=query,
                sort=[("created_at", 1)]
            )

            return rules
        except Exception as e:
            await self.handle_error(e, {
                "thesis_id": str(thesis_id),
                "exclude_from_scoring": exclude_from_scoring,
                "rule_type": rule_type
            })
            return []

    async def create_match_rule(
        self,
        thesis_id: Union[str, ObjectId],
        **kwargs
    ) -> MatchRule:
        """Create a new match rule for a thesis."""
        try:
            if isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)

            # Create rule
            rule = MatchRule(
                thesis_id=thesis_id,
                **kwargs
            )

            # Insert rule
            await rule.save()

            # Update thesis to include this rule
            thesis = await self.get_thesis(thesis_id)
            if thesis:
                thesis.match_rules.append(rule.id)
                await thesis.save(is_update=True)

            return rule
        except Exception as e:
            await self.handle_error(e, {
                "thesis_id": str(thesis_id),
                **kwargs
            })
            raise

    async def update_match_rule(
        self,
        rule_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[MatchRule]:
        """Upsert a match rule: update if exists, otherwise create new. Uses type-casting for validation."""
        try:
            if isinstance(rule_id, str):
                rule_id = ObjectId(rule_id)

            rule = await MatchRule.find_one({"_id": ObjectId(rule_id)})
            if rule:
                logger.info(f"Before update - rule: {rule}")
                rule = update_model_with_type_casting(rule, update_data)
                query = {"_id": ObjectId(rule_id)}
                await rule.update_many(query, {"$set": rule.model_dump(by_alias=True, for_db=True)})
                logger.info(f"After update - rule: {rule}")
                return rule
            else:
                # Create new rule if not found (upsert)
                return await self.create_match_rule(update_data.get('thesis_id'),
                                                  name=update_data.get('name'),
                                                  description=update_data.get('description'),
                                                  operator=update_data.get('operator', 'and'),
                                                  conditions=update_data.get('conditions', []))
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id), "update_data": update_data})
            return None

    async def delete_match_rule(self, rule_id: Union[str, ObjectId]) -> bool:
        """Delete a match rule."""
        try:
            if isinstance(rule_id, str):
                rule_id = ObjectId(rule_id)

            # Get rule
            rule = await self.get_match_rule(rule_id)
            if not rule:
                return False

            # Remove rule from thesis
            thesis = await self.get_thesis(rule.thesis_id)
            if thesis and rule.id in thesis.match_rules:
                thesis.match_rules.remove(rule.id)
                await thesis.save(is_update=True)

            # Delete rule using the delete method
            await rule.delete()

            return True
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id)})
            return False

    async def get_match_rule(self, rule_id: Union[str, ObjectId]) -> Optional[MatchRule]:
        """Get a match rule by ID."""
        try:
            return await MatchRule.get_by_id(rule_id)
        except Exception as e:
            await self.handle_error(e, {"rule_id": str(rule_id)})
            return None

    async def list_match_rules(
        self,
        thesis_id: Union[str, ObjectId]
    ) -> List[MatchRule]:
        """List match rules for a thesis."""
        try:
            # Execute query using find_many
            rules = await MatchRule.find_many(
                query={"thesis_id": ObjectId(thesis_id)},
                sort=[("created_at", 1)]
            )

            return rules
        except Exception as e:
            await self.handle_error(e, {"thesis_id": str(thesis_id)})
            return []

    async def calculate_score(
        self,
        thesis_id: Union[str, ObjectId],
        form_responses: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate score for a form response based on thesis rules."""
        try:
            if isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)

            # Get thesis with rules
            thesis = await self.get_thesis_with_rules(thesis_id)
            if not thesis:
                return {"error": "Thesis not found"}

            # Get form
            form = await self.form_service.get_form(str(thesis.form_id))
            if not form:
                return {"error": "Form not found"}

            # Initialize score data
            total_score = 0.0
            max_possible_score = 0.0
            question_scores = {}
            bonus_scores = {}

            # Process scoring rules
            for rule in thesis.scoring_rules:
                # Skip deleted rules
                if rule.is_deleted:
                    continue

                # Calculate score based on rule type
                if rule.rule_type == RuleType.SCORING:
                    # Regular scoring rule
                    score = await self._calculate_scoring_rule_score(rule, form_responses)

                    # Apply weight
                    weighted_score = score * rule.weight

                    # Add to total
                    total_score += weighted_score
                    max_possible_score += rule.weight

                    # Record question score
                    question_id = str(rule.question_id) if rule.question_id else f"rule_{rule.id}"
                    question_scores[question_id] = {
                        "raw_score": score,
                        "weight": rule.weight,
                        "weighted_score": weighted_score
                    }
                else:
                    # Bonus rule
                    if rule.bonus_points:
                        if await self._evaluate_condition(rule.condition, form_responses):
                            bonus_scores[f"bonus_{rule.id}"] = rule.bonus_points
                            total_score += rule.bonus_points

            # Calculate normalized score (0-100)
            normalized_score = 0
            if max_possible_score > 0:
                normalized_score = (total_score / max_possible_score) * 100

            return {
                "thesis_id": str(thesis_id),
                "total_score": total_score,
                "normalized_score": normalized_score,
                "max_possible_score": max_possible_score,
                "question_scores": question_scores,
                "bonus_scores": bonus_scores
            }
        except Exception as e:
            await self.handle_error(e, {
                "thesis_id": str(thesis_id),
                "form_responses": form_responses
            })
            return {"error": str(e)}

    async def _calculate_scoring_rule_score(self, rule: ScoringRule, form_responses: Dict[str, Any]) -> float:
        """Calculate score for a scoring rule based on its type and condition."""
        try:
            # Handle repeatable section aggregation
            if rule.section_id and rule.aggregation and rule.aggregation != AggregationType.NONE:
                return await self._calculate_aggregated_score(rule, form_responses)

            # Handle regular scoring rule
            if rule.question_id:
                response = form_responses.get(str(rule.question_id))
                if response is None:
                    return 0.0
                return await self._evaluate_condition(rule.condition, response)

            return 0.0
        except Exception as e:
            logger.error(f"Error calculating scoring rule score: {str(e)}", exc_info=True)
            return 0.0

    async def _calculate_aggregated_score(self, rule: ScoringRule, form_responses: Dict[str, Any]) -> float:
        """Calculate score for a repeatable section with aggregation."""
        try:
            # Get all responses for this section
            section_responses = []
            for key, value in form_responses.items():
                if isinstance(value, list) and key.startswith(f"section_{rule.section_id}"):
                    section_responses.extend(value)

            if not section_responses:
                return 0.0

            # Apply filter if present
            if rule.filter:
                section_responses = [
                    resp for resp in section_responses
                    if await self._evaluate_condition(rule.filter, resp)
                ]

            if not section_responses:
                return 0.0

            # Get individual scores
            individual_scores = []
            for response in section_responses:
                if rule.value_field:
                    value = response.get(rule.value_field)
                    if value is not None:
                        score = await self._evaluate_condition(rule.condition, value)
                        individual_scores.append(score)
                else:
                    score = await self._evaluate_condition(rule.condition, response)
                    individual_scores.append(score)

            if not individual_scores:
                return 0.0

            # Apply aggregation
            if rule.aggregation == AggregationType.AVG:
                return sum(individual_scores) / len(individual_scores)
            elif rule.aggregation == AggregationType.MIN:
                return min(individual_scores)
            elif rule.aggregation == AggregationType.MAX:
                return max(individual_scores)
            elif rule.aggregation == AggregationType.COUNT:
                return sum(1 for s in individual_scores if s > 0)
            elif rule.aggregation == AggregationType.PERCENTAGE:
                return sum(1 for s in individual_scores if s > 0) / len(individual_scores)
            elif rule.aggregation == AggregationType.ALL:
                return 1.0 if all(s > 0 for s in individual_scores) else 0.0
            elif rule.aggregation == AggregationType.ANY:
                return 1.0 if any(s > 0 for s in individual_scores) else 0.0
            elif rule.aggregation == AggregationType.SUM:
                return sum(individual_scores)
            elif rule.aggregation == AggregationType.SUM_BY_FILTER_PERCENT_OF_TOTAL:
                if rule.aggregate_threshold is not None:
                    total = len(individual_scores)
                    matching = sum(1 for s in individual_scores if s > rule.aggregate_threshold)
                    return (matching / total) * 100 if total > 0 else 0.0
                return 0.0

            return 0.0
        except Exception as e:
            logger.error(f"Error calculating aggregated score: {str(e)}", exc_info=True)
            return 0.0

    async def _evaluate_condition(self, condition: Union[FilterCondition, CompoundCondition], form_responses: Dict[str, Any]) -> bool:
        """Evaluate a condition against form responses."""
        try:
            if isinstance(condition, FilterCondition):
                # Handle simple condition
                response = form_responses.get(str(condition.question_id))
                if response is None:
                    return False
                return evaluate_condition({
                    "operator": condition.operator,
                    "value": condition.value
                }, response)
            else:
                # Handle compound condition
                if condition.operator == LogicalOperator.AND:
                    return all(
                        await self._evaluate_condition(c, form_responses)
                        for c in condition.conditions
                    )
                elif condition.operator == LogicalOperator.OR:
                    return any(
                        await self._evaluate_condition(c, form_responses)
                        for c in condition.conditions
                    )
                elif condition.operator == LogicalOperator.NOT:
                    return not await self._evaluate_condition(condition.conditions[0], form_responses)
                return False
        except Exception as e:
            logger.error(f"Error evaluating condition: {str(e)}", exc_info=True)
            return False

    async def find_matching_theses(
        self,
        form_id: Union[str, ObjectId],
        form_responses: Dict[str, Any],
        org_id: Union[str, ObjectId]
    ) -> List[InvestmentThesis]:
        """Find all theses that match a form response based on their match rules."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Get all active theses for this form and org
            query = {
                "form_id": form_id,
                "org_id": org_id,
                "is_active": True
            }
            theses = await InvestmentThesis.find_many(query=query)

            # Filter theses by match rules
            matching_theses = []
            for thesis in theses:
                # Get match rules
                match_rules = await self.list_match_rules(thesis.id)

                # If no match rules, include by default
                if not match_rules:
                    matching_theses.append(thesis)
                    continue

                # Check if any match rule is satisfied
                for rule in match_rules:
                    if await self._evaluate_match_rule(rule, form_responses):
                        matching_theses.append(thesis)
                        break

            return matching_theses
        except Exception as e:
            await self.handle_error(e, {
                "form_id": str(form_id),
                "org_id": str(org_id)
            })
            return []

    async def _evaluate_match_rule(self, rule: MatchRule, form_responses: Dict[str, Any]) -> bool:
        """Evaluate a match rule against form responses."""
        if not rule.conditions:
            return True

        # Evaluate each condition
        results = []
        for condition in rule.conditions:
            question_id = condition.get("question_id")
            if not question_id:
                continue

            response = form_responses.get(str(question_id))
            if response is None:
                results.append(False)
                continue

            # Evaluate condition
            condition_result = await self._evaluate_condition(
                {condition.get("operator", "eq"): condition.get("value")},
                response
            )
            results.append(condition_result)

        # Combine results based on operator
        if rule.operator == "and":
            return all(results)
        else:  # "or"
            return any(results)

    async def preview_form_questions(
        self,
        form_id: Union[str, ObjectId],
        thesis_id: Optional[Union[str, ObjectId]] = None,
        include_suggestions: bool = True
    ) -> Dict[str, Any]:
        """
        Preview form questions with their scoring configuration.

        Args:
            form_id: Form ID
            thesis_id: Optional thesis ID to get existing rules
            include_suggestions: Whether to include suggested configurations

        Returns:
            Dictionary with form details, questions, and configuration options
        """
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)

            # Get form with details
            form_data = await self.form_service.get_form_with_details(str(form_id))
            if not form_data:
                return {"error": "Form not found"}

            # Get thesis if provided
            thesis = None
            scoring_rules = []
            match_rules = []
            if thesis_id:
                if isinstance(thesis_id, str):
                    thesis_id = ObjectId(thesis_id)
                thesis = await self.get_thesis(thesis_id)
                if thesis:
                    scoring_rules = await self.list_scoring_rules(thesis_id)
                    match_rules = await self.list_match_rules(thesis_id)

            # Prepare result
            result = {
                "form": {
                    "id": str(form_id),
                    "name": form_data.get("name"),
                    "description": form_data.get("description", "")
                },
                "sections": [],
                "match_rules": [rule.model_dump() for rule in match_rules],
                "match_rule_templates": self._get_match_rule_templates() if include_suggestions else []
            }

            # Process sections and questions
            for section in form_data.get("sections", []):
                section_data = {
                    "id": str(section.get("_id")),
                    "name": section.get("title"),
                    "description": section.get("description", ""),
                    "is_repeatable": section.get("repeatable", False),
                    "questions": []
                }

                for question in section.get("questions", []):
                    question_id = str(question.get("_id"))

                    # Find matching rule if any
                    matching_rule = None
                    if scoring_rules:
                        for rule in scoring_rules:
                            if str(rule.question_id) == question_id:
                                matching_rule = rule
                                break

                    question_data = {
                        "id": question_id,
                        "text": question.get("label"),
                        "type": question.get("type"),
                        "required": question.get("required", False),
                        "options": question.get("options", []),
                        "section_id": str(section.get("_id")),
                        "section_name": section.get("title")
                    }

                    # Add existing rule if available
                    question_data["scoring_rule"] = matching_rule.model_dump() if matching_rule else None

                    # Add suggested configuration if requested
                    if include_suggestions:
                        question_data["suggested_config"] = self._get_suggested_config(
                            question.get("type"),
                            question.get("options", []),
                            section.get("repeatable", False)
                        )

                    section_data["questions"].append(question_data)

                result["sections"].append(section_data)

            # Add thesis info if available
            if thesis:
                result["thesis"] = {
                    "id": str(thesis.id),
                    "name": thesis.name,
                    "description": thesis.description
                }

            return result
        except Exception as e:
            await self.handle_error(e, {
                "form_id": str(form_id),
                "thesis_id": str(thesis_id) if thesis_id else None
            })
            return {"error": str(e)}

    def _get_suggested_config(self, question_type: str, options: List[Dict[str, Any]], is_repeatable: bool) -> Dict[str, Any]:
        """
        Get suggested configuration for a question based on its type.

        Args:
            question_type: Type of question
            options: Question options
            is_repeatable: Whether the question is in a repeatable section

        Returns:
            Dictionary with suggested configuration
        """
        # Base configuration
        config = {
            "weight": 1.0,
            "exclude_from_scoring": False,
            "rule_type": "scoring"
        }

        # Add repeatable-specific fields if needed
        if is_repeatable:
            config["is_repeatable"] = True

            # Suggest appropriate aggregation based on question type
            if question_type in ["number", "range", "slider"]:
                config["aggregation_type"] = "avg"
            elif question_type in ["select", "multi_select", "checkbox"]:
                config["aggregation_type"] = "count"
            elif question_type in ["text", "textarea"]:
                config["aggregation_type"] = "any_match"
            else:
                config["aggregation_type"] = "max"

            # Example filter
            config["filters"] = []
        else:
            config["is_repeatable"] = False

        # Type-specific suggestions
        if question_type == "text":
            config["condition"] = {"contains": ""}
        elif question_type == "textarea":
            config["condition"] = {"contains": ""}
        elif question_type == "number":
            config["condition"] = {"gte": 0}
        elif question_type == "select":
            if options:
                config["expected_value"] = options[0].get("value", "")
        elif question_type == "multi_select":
            if options:
                config["expected_value"] = [options[0].get("value", "")]
        elif question_type == "checkbox":
            config["expected_value"] = True
        elif question_type == "range" or question_type == "slider":
            config["expected_value"] = [0, 100]
        elif question_type == "date":
            config["condition"] = {"gte": "2023-01-01"}

        return config

    def _get_match_rule_templates(self) -> List[Dict[str, Any]]:
        """
        Get template match rules for common scenarios.

        Returns:
            List of template match rules
        """
        return [
            {
                "name": "Basic Match",
                "description": "Simple match on a single question",
                "operator": "and",
                "conditions": [
                    {
                        "question_id": "",
                        "operator": "eq",
                        "value": ""
                    }
                ]
            },
            {
                "name": "Industry Match",
                "description": "Match based on industry selection",
                "operator": "or",
                "conditions": [
                    {
                        "question_id": "",
                        "operator": "in",
                        "value": ["Technology", "Healthcare", "Finance"]
                    }
                ]
            },
            {
                "name": "Revenue and Team Size",
                "description": "Match based on revenue and team size",
                "operator": "and",
                "conditions": [
                    {
                        "question_id": "",
                        "operator": "gte",
                        "value": 100000
                    },
                    {
                        "question_id": "",
                        "operator": "gte",
                        "value": 5
                    }
                ]
            }
        ]