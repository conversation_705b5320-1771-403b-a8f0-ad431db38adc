from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from app.schemas.sharing import EmbedType, SharingType





class SharingServiceInterface(ABC):
    """Interface for sharing services."""
    
    @abstractmethod
    async def initialize(self) -> None:
        """Initialize sharing service resources."""
        pass
    
    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup sharing service resources."""
        pass
    
    @abstractmethod
    async def create_sharing_config(
        self,
        resource_type: str,
        resource_id: str,
        org_id: str,
        enabled: bool = True,
        sharing_types: List[SharingType] = None,
        allowed_domains: List[str] = None,
        embed_type: EmbedType = EmbedType.INLINE,
        custom_styles: Optional[Dict[str, Any]] = None,
        tracking_enabled: bool = True,
        expires_at: Optional[Union[datetime, int]] = None
    ) -> Dict[str, Any]:
        """
        Create a sharing configuration for a resource.
        
        Args:
            resource_type: Type of resource (e.g., 'form', 'report')
            resource_id: ID of the resource
            org_id: Organization ID
            enabled: Whether sharing is enabled
            sharing_types: List of sharing types to enable
            allowed_domains: List of domains allowed for embedding
            embed_type: Type of embed
            custom_styles: Custom styles for embedding
            tracking_enabled: Whether to track views/usage
            expires_at: When the sharing configuration expires
            
        Returns:
            Sharing configuration details
        """
        pass
    
    @abstractmethod
    async def get_sharing_config(
        self,
        config_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get sharing configuration by ID.
        
        Args:
            config_id: ID of the sharing configuration
            
        Returns:
            Sharing configuration details or None if not found
        """
        pass
    
    @abstractmethod
    async def update_sharing_config(
        self,
        config_id: str,
        updates: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Update a sharing configuration.
        
        Args:
            config_id: ID of the sharing configuration
            updates: Fields to update
            
        Returns:
            Updated sharing configuration or None if not found
        """
        pass
    
    @abstractmethod
    async def delete_sharing_config(
        self,
        config_id: str
    ) -> bool:
        """
        Delete a sharing configuration.
        
        Args:
            config_id: ID of the sharing configuration
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def generate_sharing_link(
        self,
        config_id: str,
        created_by: str,
        expires_at: Optional[Union[datetime, int]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a sharing link.
        
        Args:
            config_id: ID of the sharing configuration
            created_by: ID of the user creating the link
            expires_at: When the link expires
            metadata: Additional metadata for the link
            
        Returns:
            Sharing link details
        """
        pass
    
    @abstractmethod
    async def generate_embed_code(
        self,
        config_id: str,
        created_by: str,
        embed_type: Optional[EmbedType] = None,
        custom_styles: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate an embed code.
        
        Args:
            config_id: ID of the sharing configuration
            created_by: ID of the user creating the embed code
            embed_type: Type of embed (overrides config if provided)
            custom_styles: Custom styles (overrides config if provided)
            metadata: Additional metadata for the embed code
            
        Returns:
            Embed code details
        """
        pass
    
    @abstractmethod
    async def generate_qr_code(
        self,
        config_id: str,
        created_by: str,
        size: int = 300,
        error_correction_level: str = "M",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Generate a QR code.
        
        Args:
            config_id: ID of the sharing configuration
            created_by: ID of the user creating the QR code
            size: Size of the QR code in pixels
            error_correction_level: Error correction level (L, M, Q, H)
            metadata: Additional metadata for the QR code
            
        Returns:
            QR code details
        """
        pass
    
    @abstractmethod
    async def list_sharing_links(
        self,
        config_id: str
    ) -> List[Dict[str, Any]]:
        """
        List sharing links for a configuration.
        
        Args:
            config_id: ID of the sharing configuration
            
        Returns:
            List of sharing link details
        """
        pass

    @abstractmethod
    async def get_sharing_link(
        self,
        link_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get sharing link by ID.
        
        Args:
            link_id: ID of the sharing link
            
        Returns:
            Sharing link details or None if not found
        """
        pass
    
    @abstractmethod
    async def get_embed_code(
        self,
        embed_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get embed code by ID.
        
        Args:
            embed_id: ID of the embed code
            
        Returns:
            Embed code details or None if not found
        """
        pass
    
    @abstractmethod
    async def get_qr_code(
        self,
        qr_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get QR code by ID.
        
        Args:
            qr_id: ID of the QR code
            
        Returns:
            QR code details or None if not found
        """
        pass
    
    @abstractmethod
    async def track_view(
        self,
        sharing_id: str,
        sharing_type: SharingType,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Track a view of a shared resource.
        
        Args:
            sharing_id: ID of the sharing link, embed code, or QR code
            sharing_type: Type of sharing
            metadata: Additional metadata for the view
            
        Returns:
            True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_sharing_stats(
        self,
        resource_type: str,
        resource_id: str
    ) -> Dict[str, Any]:
        """
        Get sharing statistics for a resource.
        
        Args:
            resource_type: Type of resource
            resource_id: ID of the resource
            
        Returns:
            Sharing statistics
        """
        pass
    
    @abstractmethod
    async def validate_sharing_token(
        self,
        token: str
    ) -> Optional[Dict[str, Any]]:
        """
        Validate a sharing token.
        
        Args:
            token: Sharing token
            
        Returns:
            Resource details if valid, None otherwise
        """
        pass
