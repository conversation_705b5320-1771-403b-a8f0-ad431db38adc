from typing import Optional, List, Any
import json
import logging

import redis.asyncio as redis
from redis.asyncio import Redis

from app.core.config import settings
from app.services.base import BaseService
from app.services.cache.interfaces import CacheServiceInterface

logger = logging.getLogger(__name__)

class RedisService(BaseService, CacheServiceInterface):
    """Redis cache service implementation."""
    
    def __init__(self):
        super().__init__()
        self.redis: Optional[Redis] = None
        self.prefix = settings.REDIS_KEY_PREFIX
        self.default_ttl = settings.REDIS_DEFAULT_TTL

    async def initialize(self) -> None:
        """Initialize Redis connection."""
        try:
            self.redis = await redis.from_url(
                settings.REDIS_URL,
                encoding="utf-8",
                decode_responses=True
            )
            self.logger.info("Connected to Redis")
        except Exception as e:
            await self.handle_error(e, {"error": "Failed to connect to Red<PERSON>"})

    async def cleanup(self) -> None:
        """Cleanup Redis connection."""
        if self.redis:
            await self.redis.close()
            self.logger.info("Disconnected from Redis")

    def _build_key(self, key: str, namespace: Optional[str] = None) -> str:
        """Build a namespaced key."""
        if namespace:
            return f"{self.prefix}:{namespace}:{key}"
        return f"{self.prefix}:{key}"

    async def get(self, key: str, namespace: Optional[str] = None) -> Optional[Any]:
        """Get value by key."""
        try:
            full_key = self._build_key(key, namespace)
            value = await self.redis.get(full_key)
            if value:
                return json.loads(value)
            return None
        except Exception as e:
            await self.handle_error(e, {"key": key, "error": "Error getting value from Redis"})

    async def set(
        self,
        key: str,
        value: Any,
        namespace: Optional[str] = None,
        ttl: Optional[int] = None
    ) -> bool:
        """Set value for key with optional TTL."""
        try:
            full_key = self._build_key(key, namespace)
            value_str = json.dumps(value)
            ttl = ttl or self.default_ttl
            
            if ttl:
                await self.redis.setex(full_key, ttl, value_str)
            else:
                await self.redis.set(full_key, value_str)
                
            return True
        except Exception as e:
            await self.handle_error(e, {"key": key, "error": "Error setting value in Redis"})

    async def delete(self, key: str, namespace: Optional[str] = None) -> bool:
        """Delete key from Redis."""
        try:
            full_key = self._build_key(key, namespace)
            result = await self.redis.delete(full_key)
            return bool(result)
        except Exception as e:
            await self.handle_error(e, {"key": key, "error": "Error deleting key from Redis"})

    async def list_keys(
        self,
        pattern: str = "*",
        namespace: Optional[str] = None,
        count: int = 100
    ) -> List[str]:
        """List keys using SCAN for better performance."""
        try:
            full_pattern = self._build_key(pattern, namespace)
            keys = []
            cursor = 0
            
            while True:
                cursor, found_keys = await self.redis.scan(
                    cursor,
                    match=full_pattern,
                    count=count
                )
                keys.extend(found_keys)
                
                if cursor == 0:
                    break
                    
            return keys
        except Exception as e:
            await self.handle_error(e, {"pattern": pattern, "error": "Error listing keys from Redis"})

    async def exists(self, key: str, namespace: Optional[str] = None) -> bool:
        """Check if key exists."""
        try:
            full_key = self._build_key(key, namespace)
            return bool(await self.redis.exists(full_key))
        except Exception as e:
            await self.handle_error(e, {"key": key, "error": "Error checking key existence in Redis"})

    async def ttl(self, key: str, namespace: Optional[str] = None) -> Optional[int]:
        """Get TTL for key."""
        try:
            full_key = self._build_key(key, namespace)
            ttl = await self.redis.ttl(full_key)
            return ttl if ttl > 0 else None
        except Exception as e:
            await self.handle_error(e, {"key": key, "error": "Error getting TTL from Redis"}) 