from typing import Optional, List, Dict, Any
from abc import ABC, abstractmethod
from bson import ObjectId

from app.models.form import Form, Section, Question, Submission, FormWithDetails


class FormServiceInterface(ABC):
    """Interface for form services."""

    @abstractmethod
    async def create_form(
        self,
        name: str,
        description: str,
        sections: List[ObjectId],
        default_section_ids: List[ObjectId],
        is_active: bool = True,
        org_id: Optional[str] = None,
        user_id: Optional[str] = None
    ) -> Form:
        """Create a new form."""
        pass

    @abstractmethod
    async def list_forms(self, org_id: str) -> List[Dict[str, Any]]:
        """List all forms in the organization."""
        pass

    @abstractmethod
    async def get_form(self, form_id: str) -> Optional[Form]:
        """Get form by ID."""
        pass

    @abstractmethod
    async def get_form_with_details(self, form_id: str) -> FormWithDetails:
        """Get form with nested sections and questions."""
        pass

    @abstractmethod
    async def create_form_version(self, form_id: str, actor_id: Optional[str] = None) -> Optional[Form]:
        """
        Create a new version of an existing form.

        Args:
            form_id: ID of the form to version
            actor_id: Optional ID of the user performing the operation

        Returns:
            The new form, or None if the form doesn't exist
        """
        pass

    @abstractmethod
    async def get_form_version_history(self, form_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Get version history for a form.

        Args:
            form_id: ID of the form
            limit: Maximum number of versions to return (default: 10)

        Returns:
            List of version snapshots, ordered by version (descending)
        """
        pass

    @abstractmethod
    async def rollback_form_to_version(
        self,
        form_id: str,
        target_version: int,
        actor_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Roll back a form to a specific version.

        Args:
            form_id: ID of the form to roll back
            target_version: Version to roll back to
            actor_id: Optional ID of the user performing the rollback

        Returns:
            The rolled-back form, or None if the version doesn't exist
        """
        pass

    @abstractmethod
    async def update_form(
        self,
        form_id: str,
        update: Dict[str, Any],
        actor_id: Optional[str] = None
    ) -> Optional[Form]:
        """
        Update form metadata with versioning support.

        Args:
            form_id: ID of the form to update
            update: Dictionary of updates to apply
            actor_id: Optional ID of the user performing the update

        Returns:
            The updated form, or None if the form doesn't exist
        """
        pass

    @abstractmethod
    async def archive_form(self, form_id: str, actor_id: Optional[str] = None) -> bool:
        """
        Archive a form with versioning support.

        Args:
            form_id: ID of the form to archive
            actor_id: Optional ID of the user performing the archive

        Returns:
            True if the form was successfully archived, False otherwise
        """
        pass
    
    @abstractmethod
    async def delete_form(self, form_id: str) -> bool:
        """Delete a form."""
        pass

    @abstractmethod
    async def create_section(
        self,
        form_id: str,
        create_data: Dict[str, Any]
    ) -> Section:
        """Create a new section."""
        pass

    @abstractmethod
    async def get_section_with_questions(self, section_id: str) -> Optional[Dict[str, Any]]:
        """Get section with all its questions."""
        pass

    @abstractmethod
    async def update_section(
        self,
        section_id: str,
        update: Dict[str, Any]
    ) -> Optional[Section]:
        """Update a section."""
        pass

    @abstractmethod
    async def delete_section(self, section_id: str) -> bool:
        """Delete a section."""
        pass

    @abstractmethod
    async def create_question(
        self,
        section_id: str,
        create_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a new question.

        Enhanced to support repeatable section embedding through the repeat_section_id
        and max_repeats parameters.

        Args:
            section_id: ID of the section to add the question to
            create_data: Dictionary containing question data

        Returns:
            The complete form with all details (sections and questions) after creating the question
        """
        pass

    @abstractmethod
    async def get_question(self, question_id: str) -> Optional[Question]:
        """Get question by ID."""
        pass

    @abstractmethod
    async def update_question(
        self,
        question_id: str,
        update: Dict[str, Any]
    ) -> Optional[Question]:
        """Update a question."""
        pass

    @abstractmethod
    async def delete_question(self, question_id: str) -> bool:
        """Delete a question."""
        pass

    @abstractmethod
    async def submit_form(
        self,
        form_id: str,
        answers: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> Submission:
        """
        Submit a form with answers.

        Args:
            form_id: ID of the form to submit
            answers: Dictionary of answers (question_id -> answer)
            metadata: Optional metadata to include with the submission

        Returns:
            The created submission
        """
        pass

    @abstractmethod
    async def get_submission(self, submission_id: str) -> Optional[Submission]:
        """Get a submission by ID."""
        pass

    @abstractmethod
    async def update_submission(
        self,
        submission_id: str,
        answers: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        status: Optional[str] = None
    ) -> Optional[Submission]:
        """
        Update an existing submission.

        Args:
            submission_id: ID of the submission to update
            answers: Optional new answers to update
            metadata: Optional metadata to update
            status: Optional status to update

        Returns:
            The updated submission, or None if not found
        """
        pass

    @abstractmethod
    async def get_form_submissions(
        self,
        form_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Submission]:
        """Get all submissions for a form."""
        pass

    @abstractmethod
    async def get_sequential_form(
        self,
        form_id: str,
        answers: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Get form in a sequential format for filling.

        Returns the form with sections and questions ordered based on dependencies,
        and only includes questions that are currently visible based on answers.
        """
        pass

    @abstractmethod
    async def validate_submission(
        self,
        form_id: str,
        answers: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Validate a submission against form rules."""
        pass

    @abstractmethod
    async def process_submission(
        self,
        org_id: str,
        form_id: str,
        submission_id: str,
        thesis_values: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a form submission, including scoring against thesis values if provided.
        This method will score the submission even if it was excluded by filters.

        Args:
            org_id: Organization ID
            form_id: Form ID
            submission_id: Submission ID
            thesis_values: Optional dictionary of thesis values to score against (question_id -> thesis_value)

        Returns:
            Dictionary containing:
            - submission: The submission data
            - scores: Dictionary of question scores (if thesis_values provided)
            - total_score: Overall score (if thesis_values provided)
            - excluded: Whether the submission was excluded by filters
            - exclusion_details: Details about exclusion if excluded

        Raises:
            ValueError: If submission or form not found
            RuntimeError: If scoring fails for any reason
        """
        pass

    @abstractmethod
    async def get_form_engine_data(
        self,
        form_id: str,
        answers: Dict[str, Any],
        mode: str,
        current_question_id: Optional[str] = None,
        include_dependencies: bool = True,
        include_validation: bool = True,
        include_visibility_rules: bool = True
    ) -> Dict[str, Any]:
        """
        Unified form engine API that supports both sequential and full form modes.

        Args:
            form_id: The ID of the form to process
            answers: Current answers to form questions
            mode: The processing mode ("sequential", "full", or "next")
            current_question_id: The current question ID (for "next" mode)
            include_dependencies: Whether to include dependency metadata
            include_validation: Whether to include validation rules
            include_visibility_rules: Whether to include visibility conditions

        Returns:
            A dictionary containing the processed form data based on the requested mode
        """
        pass
    
    @abstractmethod
    async def reorder_sections(self, form_id: str, items: List[Dict[str, Any]]) -> bool:
        """Reorder sections."""
        pass
    
    @abstractmethod
    async def reorder_questions(self, section_id: str, items: List[Dict[str, Any]]) -> bool:
        """Reorder questions."""
        pass