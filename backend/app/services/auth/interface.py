from abc import ABC, abstractmethod
from typing import Optional, Union
from app.models.token import Token
from app.models.user import User, PublicUser
from app.schemas.auth import TokenResponse


class IAuthService(ABC):
    @abstractmethod
    async def authenticate_user(self, email: str, password: str) -> Optional[User]:
        pass

    @abstractmethod
    async def send_password_reset_email(self, email: str) -> None:
        pass

    @abstractmethod
    async def reset_password(self, token: str, new_password: str) -> None:
        pass

    @abstractmethod
    async def invite_user(self, email: str, role_id: str, org_id: str, invited_by: str) -> dict:
        pass

    @abstractmethod
    async def accept_invitation(self, token: str, password: str) -> None:
        pass

    @abstractmethod
    async def get_redirect_url(self, org) -> str:
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        pass

    @abstractmethod
    async def create_tokens(self, user: User) -> TokenResponse:
        pass

    @abstractmethod
    async def create_public_user_tokens(self, public_user: PublicUser) -> TokenResponse:
        pass

    @abstractmethod
    async def refresh_tokens(self, refresh_token: str) -> TokenResponse:
        pass

    @abstractmethod
    async def verify_token(self, token: str) -> Union[Token, None]:
        pass

    @abstractmethod
    async def revoke_refresh_token(self, refresh_token: str) -> None:
        pass

    @abstractmethod
    async def revoke_all_user_tokens(self, user_id: str) -> None:
        pass

    @abstractmethod
    async def create_magic_link_token(self, user_id: str, email: str, redirect_url: Optional[str] = None) -> str:
        pass

    @abstractmethod
    async def verify_magic_link_token(self, token: str) -> Optional[dict]:
        pass

    @abstractmethod
    async def send_magic_link_email(self, email: str, token: str, redirect_url: Optional[str] = None) -> None:
        pass
