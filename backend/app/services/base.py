import logging
from abc import ABC, abstractmethod

from app.core.errors import DatabaseError, handle_error

logger = logging.getLogger(__name__)

class BaseService(ABC):
    """Base service class with common functionality."""
    
    def __init__(self, *args, **kwargs):
        self.logger = logging.getLogger(self.__class__.__name__)

    async def handle_error(self, error: Exception, context: dict) -> None:
        """Handle service errors consistently."""
        error = DatabaseError(
            message=str(error),
            details=context,
            should_alert=True
        )
        await handle_error(error)
        raise error.to_http_exception()

    @abstractmethod
    async def initialize(self) -> None:
        """Initialize service resources."""
        pass

    @abstractmethod
    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass 