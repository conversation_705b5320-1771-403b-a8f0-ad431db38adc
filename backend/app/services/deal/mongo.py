"""
MongoDB Implementation of Deal Service

This module implements the DealServiceInterface using MongoDB as the data store.
"""

from typing import List, Optional, Dict, Any, Union, Tuple
from bson import ObjectId
from datetime import datetime, timezone
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.logging import get_logger
from app.models.deal import Deal, DealStatus
from app.models.form import Form, Question, CoreFieldType
from app.services.base import BaseService
from app.services.deal.interfaces import DealServiceInterface
from app.services.thesis.interfaces import ThesisServiceInterface
from app.services.factory import get_thesis_service
from app.utils.model.type_casting import update_model_with_type_casting

logger = get_logger(__name__)


class DealService(BaseService, DealServiceInterface):
    """MongoDB implementation of DealService."""

    def __init__(self, db: Optional[AsyncIOMotorDatabase] = None):
        super().__init__(db)
        self.thesis_service: Optional[ThesisServiceInterface] = None

    async def initialize(self) -> None:
        """Initialize service dependencies."""
        self.thesis_service = await get_thesis_service()

    async def cleanup(self) -> None:
        """Cleanup service resources."""
        pass

    # Core CRUD Operations
    async def create_deal(
        self,
        org_id: Union[str, ObjectId],
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        deal_data: Optional[Dict[str, Any]] = None
    ) -> Optional[Deal]:
        """Create a new deal directly."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)

            # Prepare deal data
            deal_dict = {
                "org_id": org_id,
                "form_id": form_id,
                "submission_ids": [submission_id],
                "created_by": created_by,
                **(deal_data or {})
            }

            # Create deal
            deal = Deal(**deal_dict)

            # Add initial timeline event
            deal.add_timeline_event("Deal created")

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} directly")
            
            return deal
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "form_id": str(form_id),
                "submission_id": str(submission_id),
                "created_by": str(created_by),
                "deal_data": deal_data
            })
            return None

    async def create_deal_from_submission(
        self,
        form_id: Union[str, ObjectId],
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        created_by: Union[str, ObjectId],
        form: Optional[Form] = None,
        submission_data: Optional[Dict[str, Any]] = None,
        thesis_id: Optional[Union[str, ObjectId]] = None
    ) -> Optional[Deal]:
        """Create a new deal from a form submission."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)
            if isinstance(created_by, str):
                created_by = ObjectId(created_by)
            if thesis_id and isinstance(thesis_id, str):
                thesis_id = ObjectId(thesis_id)

            # Get form if not provided
            if not form:
                form = await Form.get_by_id(form_id)
                if not form:
                    logger.error(f"Form not found: {form_id}")
                    return None

            # Get submission data if not provided
            if not submission_data:
                # TODO: Get submission data from submission service
                # For now, we'll assume it's provided
                logger.error("Submission data must be provided")
                return None

            # Extract core fields from submission
            core_fields = await self._extract_core_fields(form, submission_data)
            
            # Create deal
            deal = Deal(
                org_id=org_id,
                form_id=form_id,
                submission_ids=[submission_id],
                created_by=created_by,
                **core_fields
            )

            # Add initial timeline event
            deal.add_timeline_event("Deal created from submission")

            # Calculate scoring if thesis provided
            if thesis_id and self.thesis_service:
                scoring_result = await self.thesis_service.calculate_score(
                    thesis_id,
                    submission_data
                )
                if scoring_result and "error" not in scoring_result:
                    deal.scoring = scoring_result
                    deal.add_timeline_event(
                        "Initial scoring completed",
                        f"Score: {scoring_result.get('normalized_score', 0)}"
                    )

            # Save deal
            await deal.save()
            logger.info(f"Created deal {deal.id} from submission {submission_id}")
            
            return deal
        except Exception as e:
            await self.handle_error(e, {
                "form_id": str(form_id),
                "submission_id": str(submission_id),
                "org_id": str(org_id),
                "created_by": str(created_by),
                "thesis_id": str(thesis_id) if thesis_id else None
            })
            return None

    async def _extract_core_fields(
        self,
        form: Form,
        submission_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Extract core fields from submission data based on form questions."""
        core_fields = {
            "company_name": None,
            "stage": None,
            "sector": None
        }

        # Get all questions from form
        questions = []
        for section in form.sections:
            section_questions = await Question.find_many(
                query={"section_id": section.id},
                sort=[("order", 1)]
            )
            questions.extend(section_questions)

        # Extract core fields from submission
        for question in questions:
            if not question.core_field:
                continue

            answer = submission_data.get(str(question.id))
            if answer is None:
                continue

            if question.core_field == CoreFieldType.COMPANY_NAME:
                core_fields["company_name"] = str(answer)
            elif question.core_field == CoreFieldType.STAGE:
                core_fields["stage"] = str(answer)
            elif question.core_field == CoreFieldType.SECTOR:
                if question.type == "multi_select":
                    core_fields["sector"] = answer if isinstance(answer, list) else [str(answer)]
                else:
                    core_fields["sector"] = str(answer)

        return core_fields

    async def get_deal(self, deal_id: Union[str, ObjectId]) -> Optional[Deal]:
        """Get a deal by ID."""
        try:
            return await Deal.get_by_id(deal_id)
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return None

    async def list_deals(
        self,
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100,
        status: Optional[DealStatus] = None,
        form_id: Optional[Union[str, ObjectId]] = None,
        search: Optional[str] = None,
        stage: Optional[str] = None,
        sector: Optional[str] = None,
        tags: Optional[List[str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Tuple[List[Deal], int]:
        """List deals with optional filtering and pagination."""
        try:
            # Build query
            query = {"org_id": ObjectId(org_id)}
            
            if status:
                query["status"] = status
            if form_id:
                query["form_id"] = ObjectId(form_id)
            if search:
                query["company_name"] = {"$regex": search, "$options": "i"}
            if stage:
                query["stage"] = stage
            if sector:
                query["sector"] = {"$in": [sector] if isinstance(sector, str) else sector}
            if tags:
                query["tags"] = {"$in": tags}

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(
                query=query,
                skip=skip,
                limit=limit,
                sort=sort
            )

            return deals, total
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "skip": skip,
                "limit": limit,
                "status": status.value if status else None,
                "form_id": str(form_id) if form_id else None,
                "search": search,
                "stage": stage,
                "sector": sector,
                "tags": tags,
                "sort_by": sort_by,
                "sort_order": sort_order
            })
            return [], 0

    async def update_deal(
        self,
        deal_id: Union[str, ObjectId],
        update_data: Dict[str, Any]
    ) -> Optional[Deal]:
        """Update a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Handle status updates
            if "status" in update_data:
                new_status = DealStatus(update_data["status"])
                notes = update_data.get("status_notes")
                deal.update_status(new_status, notes)
                del update_data["status"]
                if "status_notes" in update_data:
                    del update_data["status_notes"]

            # Handle tag updates
            if "add_tags" in update_data:
                for tag in update_data["add_tags"]:
                    deal.add_tag(tag)
                del update_data["add_tags"]
            if "remove_tags" in update_data:
                for tag in update_data["remove_tags"]:
                    deal.remove_tag(tag)
                del update_data["remove_tags"]

            # Update other fields
            for key, value in update_data.items():
                if hasattr(deal, key):
                    setattr(deal, key, value)

            # Save updates
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            await deal.save(is_update=True)

            return deal
        except Exception as e:
            await self.handle_error(e, {
                "deal_id": str(deal_id),
                "update_data": update_data
            })
            return None

    async def delete_deal(self, deal_id: Union[str, ObjectId]) -> bool:
        """Delete a deal."""
        try:
            if isinstance(deal_id, str):
                deal_id = ObjectId(deal_id)

            # Get deal
            deal = await self.get_deal(deal_id)
            if not deal:
                return False

            # Delete deal
            await deal.delete()
            return True
        except Exception as e:
            await self.handle_error(e, {"deal_id": str(deal_id)})
            return False

    # Auxiliary Operations
    async def get_deals_by_submission(
        self,
        submission_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId]
    ) -> List[Deal]:
        """Get deals associated with a specific submission."""
        try:
            if isinstance(submission_id, str):
                submission_id = ObjectId(submission_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {
                "org_id": org_id,
                "submission_ids": submission_id
            }

            deals = await Deal.find_many(
                query=query,
                sort=[("created_at", -1)]
            )

            return deals
        except Exception as e:
            await self.handle_error(e, {
                "submission_id": str(submission_id),
                "org_id": str(org_id)
            })
            return []

    async def get_deals_by_form(
        self,
        form_id: Union[str, ObjectId],
        org_id: Union[str, ObjectId],
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Deal], int]:
        """Get deals associated with a specific form."""
        try:
            if isinstance(form_id, str):
                form_id = ObjectId(form_id)
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            query = {
                "org_id": org_id,
                "form_id": form_id
            }

            # Get total count
            total = await Deal.count(query)

            # Execute query
            deals = await Deal.find_many(
                query=query,
                skip=skip,
                limit=limit,
                sort=[("created_at", -1)]
            )

            return deals, total
        except Exception as e:
            await self.handle_error(e, {
                "form_id": str(form_id),
                "org_id": str(org_id),
                "skip": skip,
                "limit": limit
            })
            return [], 0

    async def get_deal_summary(
        self,
        org_id: Union[str, ObjectId]
    ) -> Dict[str, Any]:
        """Get deal summary statistics for dashboard."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Get all deals for the organization
            deals = await Deal.find_many(query={"org_id": org_id})

            # Calculate summary statistics
            total_deals = len(deals)
            
            # Count by status
            by_status = {}
            for status in DealStatus:
                by_status[status.value] = sum(1 for deal in deals if deal.status == status)

            # Count by stage
            by_stage = {}
            for deal in deals:
                if deal.stage:
                    by_stage[deal.stage] = by_stage.get(deal.stage, 0) + 1

            # Count by sector
            by_sector = {}
            for deal in deals:
                if deal.sector:
                    sectors = deal.sector if isinstance(deal.sector, list) else [deal.sector]
                    for sector in sectors:
                        by_sector[sector] = by_sector.get(sector, 0) + 1

            # Recent activity (last 7 and 30 days)
            now = datetime.now(timezone.utc)
            seven_days_ago = int((now - datetime.timedelta(days=7)).timestamp())
            thirty_days_ago = int((now - datetime.timedelta(days=30)).timestamp())

            recent_activity = {
                "last_7_days": sum(1 for deal in deals if deal.created_at >= seven_days_ago),
                "last_30_days": sum(1 for deal in deals if deal.created_at >= thirty_days_ago)
            }

            return {
                "total_deals": total_deals,
                "by_status": by_status,
                "by_stage": by_stage,
                "by_sector": by_sector,
                "recent_activity": recent_activity
            }
        except Exception as e:
            await self.handle_error(e, {"org_id": str(org_id)})
            return {
                "total_deals": 0,
                "by_status": {},
                "by_stage": {},
                "by_sector": {},
                "recent_activity": {"last_7_days": 0, "last_30_days": 0}
            }

    async def add_timeline_event(
        self,
        deal_id: Union[str, ObjectId],
        event: str,
        notes: Optional[str] = None,
        user_id: Optional[Union[str, ObjectId]] = None
    ) -> Optional[Deal]:
        """Add a timeline event to a deal."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            # Add timeline event with user info if provided
            event_data = {
                "date": datetime.now(timezone.utc).isoformat(),
                "event": event,
                "notes": notes
            }
            if user_id:
                event_data["user_id"] = str(user_id)

            deal.timeline.append(event_data)
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            
            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(e, {
                "deal_id": str(deal_id),
                "event": event,
                "notes": notes,
                "user_id": str(user_id) if user_id else None
            })
            return None

    async def update_deal_notes(
        self,
        deal_id: Union[str, ObjectId],
        notes: str
    ) -> Optional[Deal]:
        """Update deal notes."""
        try:
            deal = await self.get_deal(deal_id)
            if not deal:
                return None

            deal.notes = notes
            deal.updated_at = int(datetime.now(timezone.utc).timestamp())
            
            await deal.save(is_update=True)
            return deal
        except Exception as e:
            await self.handle_error(e, {
                "deal_id": str(deal_id),
                "notes": notes
            })
            return None

    async def bulk_update_deals(
        self,
        deal_ids: List[Union[str, ObjectId]],
        update_data: Dict[str, Any],
        org_id: Union[str, ObjectId]
    ) -> List[Deal]:
        """Bulk update multiple deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            updated_deals = []
            
            for deal_id in deal_ids:
                if isinstance(deal_id, str):
                    deal_id = ObjectId(deal_id)

                # Get deal and verify it belongs to the organization
                deal = await self.get_deal(deal_id)
                if not deal or deal.org_id != org_id:
                    continue

                # Update the deal
                updated_deal = await self.update_deal(deal_id, update_data)
                if updated_deal:
                    updated_deals.append(updated_deal)

            return updated_deals
        except Exception as e:
            await self.handle_error(e, {
                "deal_ids": [str(id) for id in deal_ids],
                "update_data": update_data,
                "org_id": str(org_id)
            })
            return []

    async def search_deals(
        self,
        org_id: Union[str, ObjectId],
        query: Optional[str] = None,
        filters: Optional[Dict[str, List[str]]] = None,
        date_range: Optional[Dict[str, str]] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc",
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Deal], int]:
        """Advanced search for deals."""
        try:
            if isinstance(org_id, str):
                org_id = ObjectId(org_id)

            # Build base query
            search_query = {"org_id": org_id}

            # Add text search
            if query:
                search_query["company_name"] = {"$regex": query, "$options": "i"}

            # Add filters
            if filters:
                if "status" in filters:
                    search_query["status"] = {"$in": filters["status"]}
                if "stage" in filters:
                    search_query["stage"] = {"$in": filters["stage"]}
                if "sector" in filters:
                    search_query["sector"] = {"$in": filters["sector"]}
                if "tags" in filters:
                    search_query["tags"] = {"$in": filters["tags"]}

            # Add date range filter
            if date_range:
                date_filter = {}
                if "start" in date_range:
                    start_date = datetime.fromisoformat(date_range["start"]).replace(tzinfo=timezone.utc)
                    date_filter["$gte"] = int(start_date.timestamp())
                if "end" in date_range:
                    end_date = datetime.fromisoformat(date_range["end"]).replace(tzinfo=timezone.utc)
                    date_filter["$lte"] = int(end_date.timestamp())
                if date_filter:
                    search_query["created_at"] = date_filter

            # Build sort
            sort_direction = 1 if sort_order == "asc" else -1
            sort = [(sort_by, sort_direction)]

            # Get total count
            total = await Deal.count(search_query)

            # Execute search
            deals = await Deal.find_many(
                query=search_query,
                skip=skip,
                limit=limit,
                sort=sort
            )

            return deals, total
        except Exception as e:
            await self.handle_error(e, {
                "org_id": str(org_id),
                "query": query,
                "filters": filters,
                "date_range": date_range,
                "sort_by": sort_by,
                "sort_order": sort_order,
                "skip": skip,
                "limit": limit
            })
            return [], 0 