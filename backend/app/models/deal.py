from datetime import datetime, timezone
from typing import List, Optional, Dict, Any, Union, Annotated
from enum import Enum
from bson import ObjectId

from pydantic import Field, ConfigDict
from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.user import User
from app.utils.common import ObjectIdField
from app.utils.model.helper import populate_reference


class DealStatus(str, Enum):
    """Status of a deal in the investor's pipeline."""
    NEW = "new"
    TRIAGE = "triage"
    REVIEWED = "reviewed"
    REJECTED = "rejected"
    APPROVED = "approved"
    NEGOTIATING = "negotiating"
    CLOSED = "closed"


class Deal(TractionXModel):
    """
    Represents a deal created from a form submission.
    Contains core information needed for dashboard display and deal tracking.
    """
    model_config = ConfigDict(
        extra_model_config={
            "json_schema_extra": {
                "example": {
                    "company_name": "Acme Corp",
                    "stage": "Series A",
                    "sector": ["Technology", "SaaS"],
                    "status": "new",
                    "form_id": "form123",
                    "submission_ids": ["sub123"],
                    "exclusion_filter_result": {
                        "excluded": False,
                        "reason": None
                    },
                    "scoring": {
                        "total_score": 85,
                        "normalized_score": 85.0,
                        "thesis_matches": ["thesis1", "thesis2"]
                    },
                    "notes": "Promising early-stage SaaS company",
                    "tags": ["AI", "B2B"],
                    "timeline": [
                        {
                            "date": "2024-03-20T10:00:00Z",
                            "event": "Initial submission",
                            "notes": "Submitted through website"
                        }
                    ]
                }
            }
        }
    )

    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    org_id: Annotated[ObjectIdField, populate_reference(Organization)]
    form_id: Annotated[ObjectIdField, populate_reference("Form")]
    submission_ids: List[ObjectIdField] = Field(default_factory=list, description="List of submission IDs associated with this deal")
    
    # Core fields extracted from submissions
    company_name: Optional[str] = Field(None, description="Company name from submission")
    stage: Optional[str] = Field(None, description="Company stage from submission")
    sector: Optional[Union[str, List[str]]] = Field(None, description="Company sector(s) from submission")
    
    # Deal tracking fields
    status: DealStatus = Field(default=DealStatus.NEW, description="Current status of the deal")
    exclusion_filter_result: Optional[Dict[str, Any]] = Field(None, description="Result of any exclusion filters applied")
    scoring: Optional[Dict[str, Any]] = Field(None, description="Scoring results from thesis evaluation")
    
    # Optional tracking fields
    notes: Optional[str] = Field(None, description="Notes about the deal")
    tags: List[str] = Field(default_factory=list, description="Tags for categorizing the deal")
    timeline: List[Dict[str, Any]] = Field(default_factory=list, description="Timeline of deal events")
    
    # Metadata
    # created_by: Annotated[ObjectIdField, populate_reference(User)] = Field(..., description="User who created the deal")
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))

    def add_submission(self, submission_id: Union[str, ObjectId]) -> None:
        """Add a submission ID to the deal's submission history."""
        if isinstance(submission_id, str):
            submission_id = ObjectId(submission_id)
        if submission_id not in self.submission_ids:
            self.submission_ids.append(submission_id)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def add_timeline_event(self, event: str, notes: Optional[str] = None) -> None:
        """Add an event to the deal's timeline."""
        self.timeline.append({
            "date": datetime.now(timezone.utc).isoformat(),
            "event": event,
            "notes": notes
        })
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def update_status(self, new_status: DealStatus, notes: Optional[str] = None) -> None:
        """Update the deal's status and add a timeline event."""
        self.status = new_status
        self.add_timeline_event(f"Status changed to {new_status}", notes)
        self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def add_tag(self, tag: str) -> None:
        """Add a tag to the deal if it doesn't exist."""
        if tag not in self.tags:
            self.tags.append(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp())

    def remove_tag(self, tag: str) -> None:
        """Remove a tag from the deal."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = int(datetime.now(timezone.utc).timestamp()) 