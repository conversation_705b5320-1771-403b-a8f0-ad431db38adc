from datetime import datetime
from enum import Enum
from typing import Annotated, Optional, List

from pydantic import EmailStr, Field, model_validator

from app.models.base import TractionXModel
from app.models.organization import Organization
from app.models.role import Role
from app.utils.common import ObjectIdField
from app.utils.model.helper import populate_reference


class UserStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"
    INVITED = "invited"


class User(TractionXModel):
    """
    Represents a user in the system, including authentication, organization memberships, roles, and status.
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    name: str
    email: EmailStr
    password_hash: str = Field(default="")
    provider: str = "email"  # email, google, linkedin
    org_id: Annotated[ObjectIdField, populate_reference(
        Organization)] = Field(..., description="Default organization ID")
    org_ids: List[ObjectIdField] = Field(
        default_factory=list, description="List of organization IDs user has access to")
    role_id: Annotated[Optional[ObjectIdField],
                       populate_reference(Role)] = None
    status: UserStatus = Field(default=UserStatus.INVITED)
    is_superuser: bool = False
    is_active: bool = True
    created_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    last_login: Optional[int] = None

    @model_validator(mode="after")
    def activate_user(self) -> "User":
        if self.last_login and self.status == UserStatus.INVITED:
            self.status = UserStatus.ACTIVE
        return self


class PublicUserStatus(str, Enum):
    ACTIVE = "active"
    SUSPENDED = "suspended"


class PublicUser(TractionXModel):
    """
    Represents a public user who accesses shared forms without full system registration.
    These users are separate from the main User model and have limited access.
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    email: EmailStr
    name: Optional[str] = None
    status: PublicUserStatus = Field(default=PublicUserStatus.ACTIVE)
    is_active: bool = True
    created_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    last_access: Optional[int] = None
    # Track which shared forms this public user has accessed
    accessed_forms: List[ObjectIdField] = Field(
        default_factory=list, description="List of form IDs this public user has accessed")
    # Optional metadata for tracking
    metadata: dict = Field(default_factory=dict, description="Additional metadata for the public user")

    @classmethod
    def get_collection_name(cls) -> str:
        """Override to use 'public_users' collection."""
        return "public_users"
