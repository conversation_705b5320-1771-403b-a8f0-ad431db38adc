from typing import Optional, Dict, Any
from pydantic import Field
from datetime import datetime, timezone
from enum import Enum

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField


class SubmissionStatus(str, Enum):
    """Submission status enumeration."""
    DRAFT = "draft"
    SUBMITTED = "submitted"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"


class PublicSubmission(TractionXModel):
    """
    Represents a submission by a public user through a shared form token.
    
    This model tracks the relationship between:
    - A public user (email)
    - A sharing token (investor/form)
    - A submission (draft or submitted)
    
    Key features:
    - Only one submission per token per public user
    - Status tracking (DRAFT allows editing, SUBMITTED is read-only)
    - Token validation for access control
    - Last access tracking for session management
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    
    # Core relationships
    public_user_id: ObjectIdField = Field(..., description="ID of the public user")
    public_user_email: str = Field(..., description="Email of the public user (for quick lookup)")
    sharing_token: str = Field(..., description="The sharing token used to access the form")
    form_id: ObjectIdField = Field(..., description="ID of the form being filled")
    org_id: ObjectIdField = Field(..., description="Organization ID (from sharing config)")
    
    # Submission tracking
    submission_id: Optional[ObjectIdField] = Field(default=None, description="ID of the actual submission (if created)")
    status: SubmissionStatus = Field(default=SubmissionStatus.DRAFT, description="Current status of the submission")
    
    # Progress tracking
    answers: Dict[str, Any] = Field(default_factory=dict, description="Current form answers (draft state)")
    progress_percentage: float = Field(default=0.0, description="Completion percentage (0-100)")
    last_question_answered: Optional[str] = Field(default=None, description="ID of the last question answered")
    
    # Access control
    is_active: bool = Field(default=True, description="Whether this submission is active")
    token_expires_at: Optional[int] = Field(default=None, description="When the sharing token expires")
    last_access: Optional[int] = Field(default=None, description="Last time this submission was accessed")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")
    
    # Timestamps
    created_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    updated_at: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
    submitted_at: Optional[int] = Field(default=None, description="When the submission was finalized")
    
    def can_edit(self) -> bool:
        """Check if this submission can be edited."""
        return (
            self.is_active and 
            self.status == SubmissionStatus.DRAFT and
            (self.token_expires_at is None or self.token_expires_at > int(datetime.now(timezone.utc).timestamp()))
        )
    
    def mark_submitted(self, submission_id: str) -> None:
        """Mark this submission as submitted."""
        self.submission_id = ObjectIdField(submission_id)
        self.status = SubmissionStatus.SUBMITTED
        self.submitted_at = int(datetime.now(timezone.utc).timestamp())
        self.updated_at = int(datetime.now(timezone.utc).timestamp())
    
    def update_progress(self, answers: Dict[str, Any], progress: float, last_question: Optional[str] = None) -> None:
        """Update the progress of this submission."""
        self.answers = answers
        self.progress_percentage = min(100.0, max(0.0, progress))
        if last_question:
            self.last_question_answered = last_question
        self.last_access = int(datetime.now(timezone.utc).timestamp())
        self.updated_at = int(datetime.now(timezone.utc).timestamp())


class PublicSubmissionAccess(TractionXModel):
    """
    Tracks access attempts to public submissions for audit and security.
    """
    id: ObjectIdField = Field(default_factory=ObjectIdField, alias="_id")
    
    public_submission_id: ObjectIdField = Field(..., description="ID of the public submission")
    public_user_email: str = Field(..., description="Email of the public user")
    sharing_token: str = Field(..., description="The sharing token used")
    
    # Access details
    access_type: str = Field(..., description="Type of access (view, edit, submit, magic_link)")
    ip_address: Optional[str] = Field(default=None, description="IP address of the access")
    user_agent: Optional[str] = Field(default=None, description="User agent string")
    
    # Result
    success: bool = Field(..., description="Whether the access was successful")
    error_message: Optional[str] = Field(default=None, description="Error message if access failed")
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional access metadata")
    
    # Timestamp
    timestamp: int = Field(default_factory=lambda: int(datetime.now(timezone.utc).timestamp()))
