from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from bson import ObjectId
from pydantic import Field, field_validator
# from backend.app.models.base import TractionXModel
# from backend.app.utils.common import ObjectIdField
from app.models.base import TractionXModel
from app.utils.common import ObjectIdField


class TokenType(str, Enum):
    """
    Enumerates the types of tokens used in the system (access, refresh, etc.).
    """
    """Types of tokens in the system."""
    ACCESS = "access"
    REFRESH = "refresh"
    PASSWORD_RESET = "password_reset"
    INVITATION = "invitation"
    MAGIC_LINK = "magic_link"


class Token(TractionXModel):
    """
    Represents a token (JWT or otherwise) for authentication and authorization, including claims and DB metadata.
    """
    """Token model for both JWT claims and database storage."""
    # JWT Claims
    sub: str = Field(..., description="User ID")
    tenant: str = Field(..., description="Tenant slug")
    tier: str = Field(..., description="Subscription tier")
    exp: int = Field(..., description="Expiration time (Unix timestamp)")
    iat: int = Field(..., description="Issued at (Unix timestamp)")
    jti: str = Field(..., description="Token ID for tracking")
    type: TokenType = Field(..., description="Token type")

    # Database Fields
    id: ObjectIdField = Field(default_factory=ObjectId, alias="_id")
    token: str = Field(default="", description="The actual JWT token")
    created_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    updated_at: int = Field(default_factory=lambda: int(
        datetime.utcnow().timestamp()))
    is_revoked: bool = False
    last_used_at: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None

    # Add model validators to handle ObjectId conversion
    @field_validator('sub', 'jti', mode='before')
    @classmethod
    def convert_objectid_to_str(cls, v):
        """Convert ObjectId to string if needed."""
        if isinstance(v, ObjectId):
            return str(v)
        return v

    @field_validator('metadata', mode='before')
    @classmethod
    def convert_metadata_objectids(cls, v):
        """Convert any ObjectId values in metadata to strings."""
        if not v:
            return v

        result = {}
        for key, value in v.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            else:
                result[key] = value
        return result

    @field_validator('exp', 'iat', 'created_at', 'updated_at', 'last_used_at')
    @classmethod
    def validate_timestamp(cls, v: Any) -> int:
        """Validate and convert timestamp fields to Unix timestamps."""
        if isinstance(v, datetime):
            return int(v.timestamp())
        if isinstance(v, int):
            return v
        if isinstance(v, str):
            try:
                dt = datetime.fromisoformat(v)
                return int(dt.timestamp())
            except ValueError:
                raise ValueError(f"Invalid datetime string: {v}")
        raise ValueError(f"Invalid timestamp value: {v}")

    def to_claims(self) -> Dict[str, Any]:
        """Convert to JWT claims."""
        # Ensure sub and jti are strings, not ObjectId
        sub = str(self.sub) if self.sub else None
        jti = str(self.jti) if self.jti else None

        claims = {
            "sub": sub,
            "tenant": self.tenant,
            "tier": self.tier,
            "exp": self.exp,
            "iat": self.iat,
            "jti": jti,
            "type": self.type
        }

        # Ensure all metadata values are properly serialized
        if self.metadata:
            metadata = {}
            for k, v in self.metadata.items():
                if isinstance(v, ObjectId):
                    metadata[k] = str(v)
                else:
                    metadata[k] = v
            claims.update(metadata)

        return claims

    @classmethod
    def from_claims(cls, claims: Dict[str, Any]) -> "Token":
        """Create a Token instance from JWT claims."""
        # Extract standard claims
        sub = claims.get("sub")
        jti = claims.get("jti")

        # Ensure sub and jti are strings
        if isinstance(sub, ObjectId):
            sub = str(sub)
        if isinstance(jti, ObjectId):
            jti = str(jti)

        # Extract metadata (everything that's not a standard claim)
        metadata = {k: v for k, v in claims.items() if k not in [
            "sub", "tenant", "tier", "exp", "iat", "jti", "type"]}

        # Convert any ObjectId in metadata to strings
        for k, v in metadata.items():
            if isinstance(v, ObjectId):
                metadata[k] = str(v)

        return cls(
            sub=sub,
            tenant=claims.get("tenant"),
            tier=claims.get("tier"),
            exp=claims.get("exp"),  # Already a Unix timestamp
            iat=claims.get("iat"),  # Already a Unix timestamp
            jti=jti,
            type=claims.get("type"),
            metadata=metadata
        )

    def is_expired(self) -> bool:
        """Check if the token is expired."""
        now = int(datetime.utcnow().timestamp())
        # Ensure exp is an integer timestamp
        exp_time = self.exp
        if isinstance(exp_time, datetime):
            exp_time = int(exp_time.timestamp())
        return now > exp_time

    def to_datetime(self, field: str) -> datetime:
        """Convert a timestamp field to datetime."""
        if not hasattr(self, field):
            raise ValueError(f"Invalid field: {field}")
        return datetime.fromtimestamp(getattr(self, field))
