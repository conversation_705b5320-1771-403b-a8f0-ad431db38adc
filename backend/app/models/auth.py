from datetime import datetime
from typing import Optional
from pydantic import Field
from bson import ObjectId

from app.models.base import TractionXModel
from app.utils.common import ObjectIdField


class TokenData(TractionXModel):
    """
    Represents the payload data for an authentication token.
    """
    """Token payload data model."""
    user_id: str
    org_id: str
    role: str
    plan: str
    exp: datetime
    iat: datetime
    jti: str  # JWT ID for token tracking


class RefreshToken(TractionXModel):
    """
    Represents a refresh token stored in the database, including metadata and revocation status.
    """
    """Refresh token model for database storage."""
    id: ObjectIdField = Field(default_factory=ObjectId, alias="_id")
    user_id: str
    org_id: str
    role: str
    plan: str
    token: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    expires_at: datetime
    is_revoked: bool = False
    last_used_at: Optional[datetime] = None
    device_info: Optional[dict] = None
    ip_address: Optional[str] = None


class UserSession(TractionXModel):
    """
    Represents an active user session, including device info and refresh token linkage.
    """
    """User session model for tracking active sessions."""
    id: ObjectIdField = Field(default_factory=ObjectId, alias="_id")
    user_id: str
    org_id: str
    role: str
    plan: str
    refresh_token_id: str
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_active_at: datetime = Field(default_factory=datetime.utcnow)
    device_info: Optional[dict] = None
    ip_address: Optional[str] = None
    is_active: bool = True
