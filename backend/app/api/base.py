from typing import Callable, List, Optional, Type
from fastapi import APIRouter, Depends
from pydantic import BaseModel

from app.dependencies.org import get_org_context
from app.dependencies.auth import validate_org_header, get_current_user
from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


class BaseAPIRouter(APIRouter):
    """Base router with organization context support."""

    def __init__(
        self,
        prefix: str = "",
        tags: Optional[List[str]] = None,
        require_org: bool = True,
        disable_auth: bool = False,
        **kwargs
    ):
        # Remove custom parameters from kwargs to avoid passing them to APIRouter
        if "require_org" in kwargs:
            del kwargs["require_org"]
        if "disable_auth" in kwargs:
            del kwargs["disable_auth"]

        # Add organization header validation for all endpoints that require org context
        if require_org and not disable_auth and not settings.TEST_MODE:
            if "dependencies" not in kwargs:
                kwargs["dependencies"] = []
            kwargs["dependencies"].append(Depends(validate_org_header))

        super().__init__(prefix=prefix, tags=tags, **kwargs)
        self.require_org = require_org
        self.disable_auth = disable_auth

    def include_router(
        self,
        router: APIRouter,
        prefix: str = "",
        tags: Optional[List[str]] = None,
        **kwargs
    ) -> None:
        """Include a router with proper handling of auth and org settings."""
        # If the included router is a BaseAPIRouter, respect its settings
        if isinstance(router, BaseAPIRouter):
            # If the child router has auth disabled, modify its dependencies
            if router.disable_auth:
                # Get the router's current dependencies
                router_dependencies = []
                if hasattr(router, "dependencies"):
                    router_dependencies.extend(router.dependencies)

                # Filter out get_current_user dependencies
                router.dependencies = [
                    dep for dep in router_dependencies
                    if not (hasattr(dep, "dependency") and dep.dependency == get_current_user)
                ]

                # Also filter kwargs dependencies
                if "dependencies" in kwargs:
                    kwargs["dependencies"] = [
                        dep for dep in kwargs["dependencies"]
                        if not (hasattr(dep, "dependency") and dep.dependency == get_current_user)
                    ]

            # If the child router doesn't require org, remove org dependencies
            if not router.require_org:
                # Get the router's current dependencies
                router_dependencies = []
                if hasattr(router, "dependencies"):
                    router_dependencies.extend(router.dependencies)

                # Filter out org-related dependencies
                router.dependencies = [
                    dep for dep in router_dependencies
                    if not (hasattr(dep, "dependency") and
                            (dep.dependency == validate_org_header or
                            dep.dependency == get_org_context))
                ]

                # Also filter kwargs dependencies
                if "dependencies" in kwargs:
                    kwargs["dependencies"] = [
                        dep for dep in kwargs["dependencies"]
                        if not (hasattr(dep, "dependency") and
                                (dep.dependency == validate_org_header or
                                dep.dependency == get_org_context))
                    ]

        super().include_router(router, prefix=prefix, tags=tags, **kwargs)

    def add_api_route(
        self,
        path: str,
        endpoint: Callable,
        *,
        response_model: Optional[Type[BaseModel]] = None,
        **kwargs
    ) -> None:
        """Add a route with organization context support."""
        # If auth is disabled, remove get_current_user from endpoint dependencies
        if self.disable_auth or settings.TEST_MODE:
            # Get the endpoint's dependencies
            endpoint_dependencies = []
            if hasattr(endpoint, "dependencies"):
                endpoint_dependencies.extend(endpoint.dependencies)

            # Add any dependencies from kwargs
            if "dependencies" in kwargs and kwargs["dependencies"] is not None:
                if isinstance(kwargs["dependencies"], tuple):
                    endpoint_dependencies.extend(list(kwargs["dependencies"]))
                else:
                    endpoint_dependencies.extend(kwargs["dependencies"])

            # Filter out get_current_user dependencies
            filtered_dependencies = [
                dep for dep in endpoint_dependencies
                if not (hasattr(dep, "dependency") and dep.dependency == get_current_user)
            ]

            # Update the endpoint's dependencies
            if hasattr(endpoint, "dependencies"):
                endpoint.dependencies = filtered_dependencies
            kwargs["dependencies"] = filtered_dependencies

        # Only add org context if required and auth is not disabled
        if self.require_org and not self.disable_auth and not settings.TEST_MODE:
            # Check if endpoint has organization dependency
            has_org_dep = False
            if hasattr(endpoint, "dependencies"):
                has_org_dep = any(
                    dep.dependency == get_org_context
                    for dep in endpoint.dependencies
                )

            # Add organization dependency if not present
            if not has_org_dep:
                # Initialize dependencies list
                dependencies = []

                # Add existing dependencies if any
                if "dependencies" in kwargs and kwargs["dependencies"] is not None:
                    if isinstance(kwargs["dependencies"], tuple):
                        dependencies.extend(list(kwargs["dependencies"]))
                    else:
                        dependencies.extend(kwargs["dependencies"])

                # Add organization dependency
                dependencies.append(Depends(get_org_context))

                # Update kwargs
                kwargs["dependencies"] = dependencies

        super().add_api_route(
            path=path,
            endpoint=endpoint,
            response_model=response_model,
            **kwargs
        )
