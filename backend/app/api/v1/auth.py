from fastapi import Depends, HTTPException, status, Response

from app.api.base import BaseAPIRouter
from app.models.auth import TokenData
from app.models.user import User, PublicUser
from app.schemas.auth import (
    TokenResponse, LoginRequest, ForgotPasswordRequest,
    ResetPasswordRequest, InviteUserRequest, AcceptInvitationRequest
)
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional

from app.core.security import verify_password
from app.dependencies.auth import get_current_user
from app.services.factory import get_auth_service, get_user_service, get_public_user_service
from app.services.auth.service import AuthService, TokenType
from app.core.logging import get_logger
from app.utils.rbac.rbac import rbac_register

logger = get_logger(__name__)

public_router = BaseAPIRouter(
    prefix="",
    disable_auth=True,
    require_org=False,
    tags=["auth"]
)

protected_router = BaseAPIRouter(
    prefix="",
    tags=["auth"]
)


@public_router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    response: Response,
    auth_service=Depends(get_auth_service),
    user_service=Depends(get_user_service)
):
    """Login endpoint that returns access and refresh tokens."""
    user = await user_service.get_user_by_email(login_data.email)
    if not user or not verify_password(login_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User is inactive"
        )

    # Get user's organization and plan
    user = await user.populate("org_id")
    org = user.org_id
    if not org:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User is not associated with any organization"
        )

    # Create tokens
    token_response = await auth_service.create_tokens(user)

    logger.info(f"User inside API Router: {user}")
    # Set redirect URL based on user type
    redirect_url = await auth_service.get_redirect_url(org)
    response.headers["X-Redirect-URL"] = redirect_url
    response.headers["X-ORG-ID"] = str(org.id)

    # Check if user needs to reset password
    if user.status == "invited":
        token_response.requires_password_reset = True

    return token_response


@public_router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_token: str,
    response: Response,
    auth_service: AuthService = Depends(get_auth_service),
):
    """Refresh access token using refresh token."""
    logger.info(f"Refresh token request received")

    # Verify the refresh token first
    token_data = await auth_service.verify_token(refresh_token, TokenType.REFRESH)
    if token_data.type != TokenType.REFRESH:
        logger.error(f"Invalid token type: {token_data.type}, expected: {TokenType.REFRESH}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )

    # Get user and create new tokens (both access and refresh)
    user = await User.find_one({"_id": token_data.sub})
    if not user:
        logger.error(f"User not found for token refresh: {token_data.sub}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )

    if not user.is_active:
        logger.error(f"User is inactive: {token_data.sub}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User is inactive"
        )

    # Create new tokens (both access and refresh tokens)
    token_response = await auth_service.create_tokens(user)

    user = await user.populate("org_id")
    org = user.org_id

    logger.info(f"Token refresh successful for user: {user.id}")
    redirect_url = await auth_service.get_redirect_url(org)
    response.headers["X-Redirect-URL"] = redirect_url
    response.headers["X-ORG-ID"] = str(org.id)

    # Check if user needs to reset password
    if user.status == "invited":
        token_response.requires_password_reset = True

    return token_response


@protected_router.post("/logout")
async def logout(
    current_user: User = Depends(get_current_user),
    auth_service=Depends(get_auth_service)
):
    """Logout endpoint that revokes all tokens for the current user."""
    try:
        # Revoke all tokens for the current user
        await auth_service.revoke_all_user_tokens(str(current_user.id))
        logger.info(f"Successfully logged out user {current_user.id}")
        return {"message": "Successfully logged out"}
    except Exception as e:
        logger.error(f"Logout error for user {current_user.id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to logout"
        )


@protected_router.post("/logout-all")
async def logout_all(
    current_user: TokenData = Depends(get_current_user),
    auth_service=Depends(get_auth_service)
):
    """Logout from all devices by revoking all refresh tokens."""
    await auth_service.revoke_all_user_tokens(current_user.user_id)
    return {"message": "Successfully logged out from all devices"}


@public_router.post("/forgot-password")
async def forgot_password(
    request: ForgotPasswordRequest,
    auth_service=Depends(get_auth_service)
):
    """Send password reset email."""
    return await auth_service.send_password_reset_email(request.email)


@public_router.post("/reset-password")
async def reset_password(
    request: ResetPasswordRequest,
    auth_service=Depends(get_auth_service)
):
    """Reset password using token."""
    await auth_service.reset_password(request.token, request.password)
    return {"message": "Password successfully reset"}


@protected_router.post("/invite")
@rbac_register(resource="invite", action="create", group="Users", description="Invite new user")
async def invite_user(
    request: InviteUserRequest,
    current_user: TokenData = Depends(get_current_user),
    auth_service=Depends(get_auth_service)
):
    """Invite a new user to the organization."""
    return await auth_service.invite_user(
        name=request.name,
        email=request.email,
        role_id=request.role_id,
        org_id=request.org_id,
        invited_by=request.invited_by
    )


@public_router.post("/accept-invitation")
async def accept_invitation(
    request: AcceptInvitationRequest,
    auth_service=Depends(get_auth_service)
):
    """Accept user invitation and set password."""
    await auth_service.accept_invitation(
        token=request.token,
        password=request.password
    )
    return {"message": "Invitation accepted successfully"}


# Magic Link Authentication Models
class MagicLinkRequest(BaseModel):
    email: str = Field(..., description="Email address to send magic link to")
    redirect_url: Optional[str] = Field(None, description="URL to redirect to after verification")


class MagicLinkVerifyResponse(BaseModel):
    access_token: str
    refresh_token: str
    user: Dict[str, Any]
    org_id: Optional[str] = None


@public_router.post("/magic-link")
async def send_magic_link(
    request: MagicLinkRequest,
    auth_service=Depends(get_auth_service),
    public_user_service=Depends(get_public_user_service)
):
    """
    Send a magic link for passwordless authentication.

    This endpoint is used for the shared form flow where public users can
    optionally log in to save their progress. Uses public_users collection
    instead of the main users collection.
    """
    try:
        # Check if public user exists
        public_user = await public_user_service.get_public_user_by_email(request.email)
        if not public_user:
            logger.info(f"No public user found for email: {request.email}")
            # Create a new public user for this email
            public_user: PublicUser = await public_user_service.create_public_user(
                email=request.email,
                metadata={"source": "magic_link_request"}
            )

        logger.info(f"Public user created: {public_user}")

        # Generate magic link token
        magic_token = await auth_service.create_magic_link_token(
            user_id=str(public_user.id),
            email=public_user.email,
            redirect_url=request.redirect_url
        )

        # Send magic link email (implement in auth service)
        await auth_service.send_magic_link_email(
            email=public_user.email,
            token=magic_token,
            redirect_url=request.redirect_url
        )

        return {"message": "If the email exists in our system, a magic link has been sent."}

    except Exception as e:
        logger.error(f"Error sending magic link: {str(e)}")
        return {"message": "If the email exists in our system, a magic link has been sent."}


@public_router.get("/magic-link/verify", response_model=MagicLinkVerifyResponse)
async def verify_magic_link(
    token: str,
    auth_service=Depends(get_auth_service),
    public_user_service=Depends(get_public_user_service)
):
    """
    Verify magic link token and return authentication tokens for public users.
    """
    try:
        # Verify magic link token
        logger.info(f"Verifying magic link token: {token}")
        token_data = await auth_service.verify_magic_link_token(token)
        if not token_data:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired magic link"
            )

        # Get public user
        public_user = await public_user_service.get_public_user_by_email(token_data["email"])
        if not public_user or not public_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Public user not found or inactive"
            )

        # Update last access for the public user
        await public_user_service.update_last_access(str(public_user.id))

        # Create authentication tokens for public user
        # Note: Public users don't have organizations, so we create a limited token
        token_response = await auth_service.create_public_user_tokens(public_user)

        return MagicLinkVerifyResponse(
            access_token=token_response.access_token,
            refresh_token=token_response.refresh_token,
            user={
                "id": str(public_user.id),
                "email": public_user.email,
                "name": public_user.name or "",
                "org_id": None  # Public users don't have organizations
            },
            org_id=None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error verifying magic link: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify magic link"
        )
