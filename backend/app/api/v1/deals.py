"""
Deal API Endpoints

This module provides REST API endpoints for managing deals in the startup investing platform.
Supports full CRUD operations, filtering, search, and dashboard statistics.
"""

from typing import List, Optional
from fastapi import Depends, HTTPException, status, Query
from bson import ObjectId

from app.models.deal import Deal, DealStatus
from app.models.user import User
from app.schemas.deal import (
    DealCreate, DealUpdate, DealResponse, DealListResponse, DealSummaryResponse,
    AddTimelineEventRequest, DealNotesRequest, BulkDealUpdateRequest, DealSearchRequest
)
from app.services.deal.interfaces import DealServiceInterface
from app.services.factory import get_deal_service
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.utils.rbac.rbac import rbac_register
from app.core.logging import get_logger

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/deals", tags=["deals"])


def _deal_to_response(deal: Deal) -> DealResponse:
    """Convert Deal model to DealResponse schema."""
    return DealResponse(
        id=str(deal.id),
        org_id=str(deal.org_id),
        form_id=str(deal.form_id),
        submission_ids=[str(sid) for sid in deal.submission_ids],
        company_name=deal.company_name,
        stage=deal.stage,
        sector=deal.sector,
        status=deal.status,
        exclusion_filter_result=deal.exclusion_filter_result,
        scoring=deal.scoring,
        notes=deal.notes,
        tags=deal.tags,
        timeline=deal.timeline,
        created_at=deal.created_at,
        updated_at=deal.updated_at
    )


# Core CRUD Endpoints

@router.post("", response_model=DealResponse, status_code=status.HTTP_201_CREATED)
@rbac_register(resource="deal", action="create", group="Deals", description="Create new deal")
async def create_deal(
    request: DealCreate,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealResponse:
    """
    Create a new deal.
    
    Creates a deal with the provided information. Core fields can be provided directly
    or will be extracted from the associated submission if available.
    """
    try:
        org_id, _ = org_context
        
        # Prepare deal data
        deal_data = {
            "company_name": request.company_name,
            "stage": request.stage,
            "sector": request.sector,
            "status": request.status or DealStatus.NEW,
            "notes": request.notes,
            "tags": request.tags or [],
            "exclusion_filter_result": request.exclusion_filter_result,
            "scoring": request.scoring
        }
        
        # Remove None values
        deal_data = {k: v for k, v in deal_data.items() if v is not None}
        
        # Create deal
        deal = await deal_service.create_deal(
            org_id=org_id,
            form_id=request.form_id,
            submission_id=request.submission_id,
            created_by=current_user.id,
            deal_data=deal_data
        )
        
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create deal"
            )
        
        logger.info(f"Created deal {deal.id} for organization {org_id}")
        return _deal_to_response(deal)
        
    except Exception as e:
        logger.error(f"Error creating deal: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/{deal_id}", response_model=DealResponse)
@rbac_register(resource="deal", action="view", group="Deals", description="View deal")
async def get_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealResponse:
    """
    Get a deal by ID.
    
    Returns the full deal information including timeline, scoring, and metadata.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deal ID format"
            )
        
        # Get deal
        deal = await deal_service.get_deal(deal_id)
        if not deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deal not found"
            )
        
        # Verify organization access
        if str(deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        return _deal_to_response(deal)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("", response_model=DealListResponse)
@rbac_register(resource="deal", action="view", group="Deals", description="List deals")
async def list_deals(
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of deals to return"),
    status: Optional[DealStatus] = Query(None, description="Filter by deal status"),
    form_id: Optional[str] = Query(None, description="Filter by form ID"),
    search: Optional[str] = Query(None, description="Search by company name"),
    stage: Optional[str] = Query(None, description="Filter by company stage"),
    sector: Optional[str] = Query(None, description="Filter by sector"),
    tags: Optional[str] = Query(None, description="Filter by tags (comma-separated)"),
    sort_by: str = Query("created_at", description="Field to sort by"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="Sort order"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealListResponse:
    """
    List deals with optional filtering and pagination.
    
    Supports filtering by status, form, stage, sector, tags, and text search.
    Results are paginated and can be sorted by various fields.
    """
    try:
        org_id, _ = org_context
        
        # Parse tags if provided
        tag_list = None
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        # Get deals
        deals, total = await deal_service.list_deals(
            org_id=org_id,
            skip=skip,
            limit=limit,
            status=status,
            form_id=form_id,
            search=search,
            stage=stage,
            sector=sector,
            tags=tag_list,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        # Convert to response format
        deal_responses = [_deal_to_response(deal) for deal in deals]
        
        return DealListResponse(
            deals=deal_responses,
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + len(deals) < total
        )
        
    except Exception as e:
        logger.error(f"Error listing deals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/{deal_id}", response_model=DealResponse)
@rbac_register(resource="deal", action="edit", group="Deals", description="Update deal")
async def update_deal(
    deal_id: str,
    request: DealUpdate,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealResponse:
    """
    Update a deal.
    
    Supports updating all deal fields including status, notes, tags, and core information.
    Status changes are automatically tracked in the timeline.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deal ID format"
            )
        
        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deal not found"
            )
        
        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Prepare update data
        update_data = {}
        for field, value in request.model_dump(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        # Update deal
        updated_deal = await deal_service.update_deal(deal_id, update_data)
        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update deal"
            )
        
        logger.info(f"Updated deal {deal_id} for organization {org_id}")
        return _deal_to_response(updated_deal)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete("/{deal_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="deal", action="delete", group="Deals", description="Delete deal")
async def delete_deal(
    deal_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
):
    """
    Delete a deal.
    
    Permanently removes the deal from the system. This action cannot be undone.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deal ID format"
            )
        
        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deal not found"
            )
        
        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Delete deal
        success = await deal_service.delete_deal(deal_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to delete deal"
            )
        
        logger.info(f"Deleted deal {deal_id} for organization {org_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


# Auxiliary Endpoints

@router.get("/by_submission/{submission_id}", response_model=List[DealResponse])
@rbac_register(resource="deal", action="view", group="Deals", description="Get deals by submission")
async def get_deals_by_submission(
    submission_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> List[DealResponse]:
    """
    Get deals associated with a specific submission.
    
    Returns all deals that were created from or associated with the given submission.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(submission_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid submission ID format"
            )
        
        # Get deals
        deals = await deal_service.get_deals_by_submission(submission_id, org_id)
        
        # Convert to response format
        return [_deal_to_response(deal) for deal in deals]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deals by submission {submission_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/by_form/{form_id}", response_model=DealListResponse)
@rbac_register(resource="deal", action="view", group="Deals", description="Get deals by form")
async def get_deals_by_form(
    form_id: str,
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of deals to return"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealListResponse:
    """
    Get deals associated with a specific form.
    
    Returns paginated list of deals that were created from the given form.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(form_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid form ID format"
            )
        
        # Get deals
        deals, total = await deal_service.get_deals_by_form(form_id, org_id, skip, limit)
        
        # Convert to response format
        deal_responses = [_deal_to_response(deal) for deal in deals]
        
        return DealListResponse(
            deals=deal_responses,
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + len(deals) < total
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting deals by form {form_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get("/summary", response_model=DealSummaryResponse)
@rbac_register(resource="deal", action="view", group="Deals", description="Get deal summary")
async def get_deal_summary(
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealSummaryResponse:
    """
    Get deal summary statistics for dashboard.
    
    Returns aggregated counts by status, stage, sector, and recent activity metrics.
    """
    try:
        org_id, _ = org_context
        
        # Get summary
        summary = await deal_service.get_deal_summary(org_id)
        
        return DealSummaryResponse(**summary)
        
    except Exception as e:
        logger.error(f"Error getting deal summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/{deal_id}/timeline", response_model=DealResponse)
@rbac_register(resource="deal", action="edit", group="Deals", description="Add timeline event")
async def add_timeline_event(
    deal_id: str,
    request: AddTimelineEventRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealResponse:
    """
    Add a timeline event to a deal.
    
    Records an event in the deal's timeline with timestamp and optional notes.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deal ID format"
            )
        
        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deal not found"
            )
        
        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Add timeline event
        updated_deal = await deal_service.add_timeline_event(
            deal_id, request.event, request.notes, current_user.id
        )
        
        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to add timeline event"
            )
        
        logger.info(f"Added timeline event to deal {deal_id}")
        return _deal_to_response(updated_deal)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding timeline event to deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put("/{deal_id}/notes", response_model=DealResponse)
@rbac_register(resource="deal", action="edit", group="Deals", description="Update deal notes")
async def update_deal_notes(
    deal_id: str,
    request: DealNotesRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealResponse:
    """
    Update deal notes.
    
    Replaces the existing notes with the provided content.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId format
        try:
            ObjectId(deal_id)
        except Exception:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid deal ID format"
            )
        
        # Get existing deal to verify access
        existing_deal = await deal_service.get_deal(deal_id)
        if not existing_deal:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Deal not found"
            )
        
        # Verify organization access
        if str(existing_deal.org_id) != org_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied"
            )
        
        # Update notes
        updated_deal = await deal_service.update_deal_notes(deal_id, request.notes)
        
        if not updated_deal:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to update deal notes"
            )
        
        logger.info(f"Updated notes for deal {deal_id}")
        return _deal_to_response(updated_deal)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating notes for deal {deal_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/bulk_update", response_model=List[DealResponse])
@rbac_register(resource="deal", action="edit", group="Deals", description="Bulk update deals")
async def bulk_update_deals(
    request: BulkDealUpdateRequest,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> List[DealResponse]:
    """
    Bulk update multiple deals.
    
    Applies the same updates to multiple deals in a single operation.
    Only deals belonging to the current organization will be updated.
    """
    try:
        org_id, _ = org_context
        
        # Validate ObjectId formats
        for deal_id in request.deal_ids:
            try:
                ObjectId(deal_id)
            except Exception:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid deal ID format: {deal_id}"
                )
        
        # Prepare update data
        update_data = {}
        for field, value in request.updates.model_dump(exclude_unset=True).items():
            if value is not None:
                update_data[field] = value
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No updates provided"
            )
        
        # Bulk update
        updated_deals = await deal_service.bulk_update_deals(
            request.deal_ids, update_data, org_id
        )
        
        logger.info(f"Bulk updated {len(updated_deals)} deals for organization {org_id}")
        return [_deal_to_response(deal) for deal in updated_deals]
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error bulk updating deals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.post("/search", response_model=DealListResponse)
@rbac_register(resource="deal", action="view", group="Deals", description="Advanced deal search")
async def search_deals(
    request: DealSearchRequest,
    skip: int = Query(0, ge=0, description="Number of deals to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of deals to return"),
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    deal_service: DealServiceInterface = Depends(get_deal_service)
) -> DealListResponse:
    """
    Advanced search for deals.
    
    Supports complex filtering with multiple criteria, date ranges, and custom sorting.
    """
    try:
        org_id, _ = org_context
        
        # Search deals
        deals, total = await deal_service.search_deals(
            org_id=org_id,
            query=request.query,
            filters=request.filters,
            date_range=request.date_range,
            sort_by=request.sort_by or "created_at",
            sort_order=request.sort_order or "desc",
            skip=skip,
            limit=limit
        )
        
        # Convert to response format
        deal_responses = [_deal_to_response(deal) for deal in deals]
        
        return DealListResponse(
            deals=deal_responses,
            total=total,
            skip=skip,
            limit=limit,
            has_more=skip + len(deals) < total
        )
        
    except Exception as e:
        logger.error(f"Error searching deals: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
