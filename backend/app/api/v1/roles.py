from typing import List, Optional
from fastapi import Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.dependencies.auth import get_current_user
from app.dependencies.org import get_org_context
from app.api.base import BaseAPIRouter
from app.services.factory import get_role_service
from app.services.role import RoleService
from app.models.user import User
from app.models.role import Permission
from app.utils.rbac.rbac import rbac_register
from app.core.logging import get_logger

logger = get_logger(__name__)
router = BaseAPIRouter(prefix="/roles", tags=["roles"])


@router.post("")
@rbac_register(resource="roles", action="create", group="Roles", description="Create role")
async def create_role(
    name: str,
    description: str,
    permissions: List[Permission],
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    role_service: RoleService = Depends(get_role_service)
):
    """Create a new role, optionally scoped by organization."""
    org_id, is_cross_org = org_context
    # Create role
    role = await role_service.create_role(
        name=name,
        description=description,
        permissions=permissions,
        org_id=org_id,
        user_id=str(current_user.id),
        audit_user_id=str(current_user.id),
        is_cross_org=is_cross_org
    )

    return role.model_dump()


@router.get("")
@rbac_register(resource="roles", action="view", group="Roles", description="View roles")
async def list_roles(
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    role_service: RoleService = Depends(get_role_service)
):
    """List all roles, optionally scoped by organization."""
    org_id, is_cross_org = org_context

    # Get roles
    roles = await role_service.get_all_roles(
        org_id=org_id,
    )

    return roles


@router.get("/{role_id}")
@rbac_register(resource="roles", action="view", group="Roles", description="View role")
async def get_role(
    role_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
    role_service: RoleService = Depends(get_role_service)
):
    """Get a specific role, optionally scoped by organization."""
    org_id, is_cross_org = org_context

    # Get role
    role = await role_service.get_role(
        role_id,
        org_id=org_id,
        user_id=str(current_user.id),
        is_cross_org=is_cross_org
    )
    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )

    return role.model_dump()


@router.put("/{role_id}")
@rbac_register(resource="roles", action="edit", group="Roles", description="Edit role")
async def update_role(
    role_id: str,
    name: Optional[str] = None,
    description: Optional[str] = None,
    permissions: Optional[List[Permission]] = None,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    db: AsyncIOMotorDatabase = Depends(get_database),
    role_service: RoleService = Depends(get_role_service)
):
    """Update an existing role, optionally scoped by organization."""
    org_id, is_cross_org = org_context

    # Update role
    role = await role_service.update_role(
        role_id=role_id,
        name=name,
        description=description,
        permissions=permissions,
        org_id=org_id,
        user_id=str(current_user.id),
        audit_user_id=str(current_user.id),
        is_cross_org=is_cross_org
    )

    if not role:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found"
        )

    return role.model_dump()


@router.delete("/{role_id}")
@rbac_register(resource="roles", action="delete", group="Roles", description="Delete role")
async def delete_role(
    role_id: str,
    current_user: User = Depends(get_current_user),
    org_context: tuple[str, bool] = Depends(get_org_context),
    role_service: RoleService = Depends(get_role_service)
):
    """Delete a role, optionally scoped by organization."""
    org_id, is_cross_org = org_context

    # Delete role
    success = await role_service.delete_role(
        role_id,
        org_id=org_id,
        user_id=str(current_user.id),
        audit_user_id=str(current_user.id),
        is_cross_org=is_cross_org
    )
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Role not found or cannot be deleted"
        )

    return {"message": "Role deleted successfully"}


@router.post("/{organization_id}/create-default-roles")
@rbac_register(resource="roles", action="create", hidden=True)
async def create_default_roles(
    organization_id: str,
    role_service: RoleService = Depends(get_role_service)
):
    """Create default roles for an organization."""

    # Check permissions
    await role_service.create_default_roles(organization_id)

    return {"message": "Default roles created successfully"}
