from app.api.base import BaseAPIRouter
from app.api.v1 import (
    auth,
    users,
    organizations,
    roles,
    forms,
    thesis,
    cache,
    rbac,
    queue,
    sharing,
    triggers,
    qualifier_forms,
    exclusion_filters,
    jobs,
    deals,
    public
)


protected_api_router = BaseAPIRouter()
public_api_router = BaseAPIRouter(disable_auth=True, require_org=False)
# Include system-level routers first
protected_api_router.include_router(organizations.system_router)

# Include public auth routes
public_api_router.include_router(auth.public_router)

# Include public form routes
public_api_router.include_router(forms.public_router)

# Include public submission routes
public_api_router.include_router(public.router)

# Include protected auth routes
protected_api_router.include_router(auth.protected_router)

# Include queue routes
protected_api_router.include_router(queue.router)

# Include sharing routes
protected_api_router.include_router(sharing.router)

# Include trigger routes
protected_api_router.include_router(triggers.router)

# Include qualifier form routes
protected_api_router.include_router(qualifier_forms.router)

# Include exclusion filter routes
protected_api_router.include_router(exclusion_filters.router)

# Include other routers
protected_api_router.include_router(users.router)
protected_api_router.include_router(organizations.router)
protected_api_router.include_router(roles.router)
protected_api_router.include_router(forms.router)
protected_api_router.include_router(thesis.router)
protected_api_router.include_router(cache.router)
protected_api_router.include_router(rbac.router)
protected_api_router.include_router(jobs.router)
protected_api_router.include_router(deals.router)
