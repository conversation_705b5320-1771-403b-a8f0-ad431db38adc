from typing import List, Optional
from bson import ObjectId
from fastapi import Depends, HTTPException, status, Query
from motor.motor_asyncio import AsyncIOMotorDatabase
from datetime import datetime
from pydantic import BaseModel, Field

from app.core.database import get_database
from app.dependencies.auth import get_current_user, validate_org_header
from app.api.base import BaseAPIRouter
from app.models.user import User
from app.models.organization import Organization
from app.services.role.interfaces import RoleServiceInterface
from app.utils.common import ObjectIdField
from typing import Union
from app.models.audit import AuditLog
from app.services.factory import get_role_service
from app.utils.rbac.rbac import rbac_register

# System-level router for organization management
# NOTE: Auth is DISABLED for system_router for testing. Re-enable in production.
system_router = BaseAPIRouter(
    prefix="/system/organizations",
    tags=["organizations"],
    require_org=False,
    disable_auth=True,  # Disable both org context and auth
    dependencies=[]  # Remove all dependencies
)

# Regular router for organization operations
router = BaseAPIRouter(
    prefix="/organizations",
    tags=["organizations"],
    require_org=True,
    dependencies=[Depends(validate_org_header)]
)


# --- PATCH: Allow 'created_by' to be 'System' or ObjectId for testing ---
# Patch Organization model locally for this router (for testing)
class OrganizationTestModel(Organization):
    created_by: Union[ObjectIdField, str, None] = None


class OrganizationResolution(BaseModel):
    """Enhanced organization resolution response model."""
    org_id: str
    plan: str
    org_name: str
    subdomain_enabled: bool = True
    description: Optional[str] = None
    logo_url: Optional[str] = None
    website: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    features: dict = Field(default_factory=dict)
    branding: dict = Field(default_factory=dict)


@system_router.get("/resolve",
                   response_model=OrganizationResolution)
async def resolve_organization(
    subdomain: str = Query(..., description="Organization subdomain"),
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """Resolve organization by subdomain."""
    # Find organization by subdomain
    organization = await db.organizations.find_one({"subdomain": subdomain})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Organization with subdomain '{subdomain}' not found"
        )

    # Check if organization is active
    if not organization.get("is_active", True):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization is not active. Please contact support."
        )

    # Get organization settings
    settings = organization.get("settings", {})
    plan = settings.get("plan", "basic")
    subdomain_enabled = settings.get("subdomain_enabled", True)
    features = settings.get("features", {})
    branding = settings.get("branding", {})
    return OrganizationResolution(
        org_id=str(organization["_id"]),
        plan=plan,
        org_name=organization.get("name", ""),
        subdomain_enabled=subdomain_enabled,
        description=organization.get("description"),
        logo_url=organization.get("logo_url"),
        website=organization.get("website"),
        contact_email=organization.get("contact_email"),
        contact_phone=organization.get("contact_phone"),
        features=features,
        branding=branding
    )


@system_router.post("/", response_model=OrganizationTestModel)
async def create_organization_testing(
    org: OrganizationTestModel,
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """
    Create organization for testing. Accepts 'created_by' as ObjectId, string (e.g., 'System'), or None.
    """
    # Convert 'created_by' if string 'System' to None or handle as needed
    if isinstance(org.created_by, str) and org.created_by.lower() == "system":
        org.created_by = None
    org_dict = org.model_dump(by_alias=True, exclude_none=True)
    result = await db.organizations.insert_one(org_dict)
    org_dict["_id"] = result.inserted_id
    return org_dict


@system_router.post("", response_model=Organization)
async def create_organization(
    organization: Organization,
    db: AsyncIOMotorDatabase = Depends(get_database),
    role_service: RoleServiceInterface = Depends(get_role_service)
):
    """Create a new organization (superuser only)"""
    # Create organization
    org = Organization(**organization.model_dump(by_alias=True, exclude_none=True))
    await org.save()
    created_organization = await db.organizations.find_one({"_id": org.id})

    # Create default roles for the organization
    role_service = role_service
    gp_role, analyst_role = await role_service.create_default_roles(str(org.id))

    # Log organization creation
    audit_log = AuditLog(
        user_id=organization.created_by,  # Use system user for audit
        action="create_organization",
        entity_type="organization",
        entity_id=org.id,
        metadata={
            "name": organization.name,
            "created_by_user": "system",
            "default_roles": {
                "general_partner": str(gp_role.id),
                "analyst": str(analyst_role.id)
            }
        }
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    return Organization(**created_organization)


@system_router.get("", response_model=List[Organization])
async def list_organizations(
    db: AsyncIOMotorDatabase = Depends(get_database)
):
    """List all organizations (superuser only)"""
    organizations = await db.organizations.find().to_list(length=None)
    return [Organization(**org) for org in organizations]


@router.get("/{organization_id}", response_model=Organization)
async def get_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header)
):
    """Get a specific organization by ID (superuser only)"""
    # if not current_user.is_superuser:
    #     raise HTTPException(
    #         status_code=status.HTTP_403_FORBIDDEN,
    #         detail="Only superusers can perform this action"
    #     )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )

    return Organization(**organization)


@router.put("/{organization_id}", response_model=Organization)
@rbac_register(resource="organization", action="edit", group="Organizations", description="Update organization")
async def update_organization(
    organization_id: str,
    organization_update: Organization,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header)
):
    """Update an organization (superuser only)"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can perform this action"
        )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )

    update_data = organization_update.model_dump(exclude_unset=True)
    update_data["updated_at"] = int(datetime.utcnow().timestamp())

    await db.organizations.update_one(
        {"_id": ObjectId(organization_id)},
        {"$set": update_data}
    )

    # Log organization update
    audit_log = AuditLog(
        user_id=current_user.id,
        action="update_organization",
        entity_type="organization",
        entity_id=ObjectId(organization_id),
        metadata={"update_data": update_data}
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    updated_organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    return Organization(**updated_organization)


@router.delete("/{organization_id}", status_code=status.HTTP_204_NO_CONTENT)
@rbac_register(resource="organization", action="delete", group="Organizations", description="Delete organization")
async def delete_organization(
    organization_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database),
    org_id: str = Depends(validate_org_header)
):
    """Delete an organization (superuser only)"""
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only superusers can perform this action"
        )

    if not ObjectId.is_valid(organization_id):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid organization ID"
        )

    # Verify the requested org_id matches the header
    if organization_id != org_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Organization ID mismatch"
        )

    organization = await db.organizations.find_one({"_id": ObjectId(organization_id)})
    if not organization:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )

    # Log organization deletion
    audit_log = AuditLog(
        user_id=current_user.id,
        action="delete_organization",
        entity_type="organization",
        entity_id=ObjectId(organization_id)
    )
    await db.audit_logs.insert_one(audit_log.dict(by_alias=True))

    await db.organizations.delete_one({"_id": ObjectId(organization_id)})
    return None
