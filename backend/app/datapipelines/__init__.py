"""
TractionX Data Pipeline Service

This package provides a modular, scalable data pipeline service for orchestrating
ingestion, enrichment, merging, and storage of structured/unstructured data
relevant to companies, founders, news, and signals.

Key Features:
- Asynchronous pipelines using RQ (Redis Queue)
- Core data models for company, founder, news, and embeddings
- Enrichment flows for Company (Clay), Founder (PDL), News (Bing News), Embedding (OpenAI/Qdrant)
- Canonical ETL for merging and structuring data
- Storage in AWS RDS, S3, and Qdrant
- Webhook support for external enrichment providers
"""

from .models import (
    CompanyData,
    FounderData,
    NewsData,
    EmbeddingData,
    PipelineJobMetadata,
    PipelineStatus,
    EnrichmentSource
)

from .pipelines import (
    BasePipeline,
    CompanyEnrichmentPipeline,
    FounderEnrichmentPipeline,
    NewsAggregationPipeline,
    EmbeddingPipeline
)

from .storage import (
    StorageInterface,
    RDSStorage,
    S3Storage,
    QdrantStorage
)

from .tasks import (
    trigger_company_enrichment,
    trigger_founder_enrichment,
    trigger_news_aggregation,
    trigger_embedding_generation,
    trigger_etl_merge
)

__all__ = [
    # Models
    "CompanyData",
    "FounderData", 
    "NewsData",
    "EmbeddingData",
    "PipelineJobMetadata",
    "PipelineStatus",
    "EnrichmentSource",
    
    # Pipelines
    "BasePipeline",
    "CompanyEnrichmentPipeline",
    "FounderEnrichmentPipeline", 
    "NewsAggregationPipeline",
    "EmbeddingPipeline",
    
    # Storage
    "StorageInterface",
    "RDSStorage",
    "S3Storage", 
    "QdrantStorage",
    
    # Tasks
    "trigger_company_enrichment",
    "trigger_founder_enrichment",
    "trigger_news_aggregation", 
    "trigger_embedding_generation",
    "trigger_etl_merge"
]
