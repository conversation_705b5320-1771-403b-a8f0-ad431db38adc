# Deal API Documentation

## Overview

The Deal API provides comprehensive REST endpoints for managing deals in the startup investing platform. It supports full CRUD operations, advanced filtering, search capabilities, and dashboard statistics.

## Base URL

All Deal API endpoints are prefixed with `/api/v1/deals`

## Authentication & Authorization

- All endpoints require authentication via JWT token
- Organization-level access control is enforced
- RBAC permissions are required for each operation:
  - `deal:create` - Create new deals
  - `deal:view` - View deals and statistics
  - `deal:edit` - Update deals and add timeline events
  - `deal:delete` - Delete deals

## Core CRUD Endpoints

### 1. Create Deal

**POST** `/api/v1/deals`

Creates a new deal with the provided information.

#### Request Body

```json
{
  "org_id": "507f1f77bcf86cd799439011",
  "form_id": "507f1f77bcf86cd799439012",
  "submission_id": "507f1f77bcf86cd799439013",
  "company_name": "Acme Corp",
  "stage": "Series A",
  "sector": ["Technology", "SaaS"],
  "status": "new",
  "notes": "Promising early-stage SaaS company",
  "tags": ["AI", "B2B"],
  "exclusion_filter_result": {
    "excluded": false,
    "reason": null
  },
  "scoring": {
    "total_score": 85,
    "normalized_score": 85.0,
    "thesis_matches": ["thesis1", "thesis2"]
  }
}
```

#### Response

```json
{
  "id": "507f1f77bcf86cd799439014",
  "org_id": "507f1f77bcf86cd799439011",
  "form_id": "507f1f77bcf86cd799439012",
  "submission_ids": ["507f1f77bcf86cd799439013"],
  "company_name": "Acme Corp",
  "stage": "Series A",
  "sector": ["Technology", "SaaS"],
  "status": "new",
  "exclusion_filter_result": {
    "excluded": false,
    "reason": null
  },
  "scoring": {
    "total_score": 85,
    "normalized_score": 85.0,
    "thesis_matches": ["thesis1", "thesis2"]
  },
  "notes": "Promising early-stage SaaS company",
  "tags": ["AI", "B2B"],
  "timeline": [
    {
      "date": "2024-03-20T10:00:00Z",
      "event": "Deal created",
      "notes": null
    }
  ],
  "created_at": 1710936000,
  "updated_at": 1710936000
}
```

### 2. Get Deal by ID

**GET** `/api/v1/deals/{deal_id}`

Retrieves a specific deal by its ID.

#### Response

Same as create deal response.

### 3. List Deals

**GET** `/api/v1/deals`

Lists deals with optional filtering and pagination.

#### Query Parameters

- `skip` (int, default: 0) - Number of deals to skip
- `limit` (int, default: 100, max: 1000) - Maximum number of deals to return
- `status` (string) - Filter by deal status (new, triage, reviewed, rejected, approved, negotiating, closed)
- `form_id` (string) - Filter by form ID
- `search` (string) - Search by company name
- `stage` (string) - Filter by company stage
- `sector` (string) - Filter by sector
- `tags` (string) - Filter by tags (comma-separated)
- `sort_by` (string, default: "created_at") - Field to sort by
- `sort_order` (string, default: "desc") - Sort order (asc/desc)

#### Response

```json
{
  "deals": [
    {
      "id": "507f1f77bcf86cd799439014",
      "company_name": "Acme Corp",
      "stage": "Series A",
      "sector": ["Technology", "SaaS"],
      "status": "new",
      "created_at": 1710936000
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 100,
  "has_more": false
}
```

### 4. Update Deal

**PUT** `/api/v1/deals/{deal_id}`

Updates an existing deal. Status changes are automatically tracked in the timeline.

#### Request Body

```json
{
  "status": "reviewed",
  "notes": "Updated after initial review",
  "tags": ["AI", "B2B", "Enterprise"],
  "scoring": {
    "total_score": 92,
    "normalized_score": 92.0,
    "thesis_matches": ["thesis1", "thesis2"]
  }
}
```

#### Response

Updated deal object (same format as create response).

### 5. Delete Deal

**DELETE** `/api/v1/deals/{deal_id}`

Permanently deletes a deal. This action cannot be undone.

#### Response

`204 No Content` on success.

## Auxiliary Endpoints

### 6. Get Deals by Submission

**GET** `/api/v1/deals/by_submission/{submission_id}`

Returns all deals associated with a specific submission.

#### Response

Array of deal objects.

### 7. Get Deals by Form

**GET** `/api/v1/deals/by_form/{form_id}`

Returns paginated list of deals created from a specific form.

#### Query Parameters

- `skip` (int, default: 0)
- `limit` (int, default: 100, max: 1000)

#### Response

Same format as list deals response.

### 8. Get Deal Summary

**GET** `/api/v1/deals/summary`

Returns aggregated statistics for dashboard display.

#### Response

```json
{
  "total_deals": 150,
  "by_status": {
    "new": 45,
    "triage": 30,
    "reviewed": 25,
    "rejected": 35,
    "approved": 10,
    "negotiating": 3,
    "closed": 2
  },
  "by_stage": {
    "Pre-Seed": 60,
    "Seed": 45,
    "Series A": 30,
    "Series B": 15
  },
  "by_sector": {
    "Technology": 80,
    "Healthcare": 25,
    "Fintech": 20,
    "E-commerce": 15,
    "Other": 10
  },
  "recent_activity": {
    "last_7_days": 12,
    "last_30_days": 45
  }
}
```

### 9. Add Timeline Event

**POST** `/api/v1/deals/{deal_id}/timeline`

Adds a custom event to the deal's timeline.

#### Request Body

```json
{
  "event": "Meeting scheduled",
  "notes": "Initial pitch meeting scheduled for next week"
}
```

#### Response

Updated deal object with new timeline event.

### 10. Update Deal Notes

**PUT** `/api/v1/deals/{deal_id}/notes`

Updates the deal's notes field.

#### Request Body

```json
{
  "notes": "Strong technical team with proven track record in AI/ML"
}
```

#### Response

Updated deal object.

### 11. Bulk Update Deals

**POST** `/api/v1/deals/bulk_update`

Updates multiple deals with the same changes in a single operation.

#### Request Body

```json
{
  "deal_ids": ["507f1f77bcf86cd799439011", "507f1f77bcf86cd799439012"],
  "updates": {
    "status": "reviewed",
    "add_tags": ["batch-reviewed"]
  }
}
```

#### Response

Array of updated deal objects.

### 12. Advanced Search

**POST** `/api/v1/deals/search`

Performs advanced search with complex filtering criteria.

#### Query Parameters

- `skip` (int, default: 0)
- `limit` (int, default: 100, max: 1000)

#### Request Body

```json
{
  "query": "AI startup",
  "filters": {
    "status": ["new", "triage"],
    "stage": ["Seed", "Series A"],
    "sector": ["Technology"],
    "tags": ["AI", "B2B"]
  },
  "date_range": {
    "start": "2024-01-01",
    "end": "2024-12-31"
  },
  "sort_by": "created_at",
  "sort_order": "desc"
}
```

#### Response

Same format as list deals response.

## Data Models

### Deal Status Enum

- `new` - Newly created deal
- `triage` - Under initial review
- `reviewed` - Completed initial review
- `rejected` - Deal rejected
- `approved` - Deal approved for further consideration
- `negotiating` - In negotiation phase
- `closed` - Deal completed (invested or passed)

### Core Fields

- `company_name` - Company name (extracted from submission)
- `stage` - Company stage (e.g., "Seed", "Series A")
- `sector` - Company sector(s) (string or array)

### Tracking Fields

- `status` - Current deal status
- `exclusion_filter_result` - Results from exclusion filters
- `scoring` - Scoring results from thesis evaluation
- `notes` - Free-form notes about the deal
- `tags` - Array of tags for categorization
- `timeline` - Array of timeline events

### Timeline Event Structure

```json
{
  "date": "2024-03-20T10:00:00Z",
  "event": "Status changed to reviewed",
  "notes": "Initial review completed",
  "user_id": "507f1f77bcf86cd799439011"
}
```

## Error Responses

### 400 Bad Request

```json
{
  "detail": "Invalid deal ID format"
}
```

### 403 Forbidden

```json
{
  "detail": "Access denied"
}
```

### 404 Not Found

```json
{
  "detail": "Deal not found"
}
```

### 500 Internal Server Error

```json
{
  "detail": "Internal server error"
}
```

## Usage Examples

### Create a Deal

```bash
curl -X POST "https://api.tractionx.com/api/v1/deals" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "org_id": "507f1f77bcf86cd799439011",
    "form_id": "507f1f77bcf86cd799439012",
    "submission_id": "507f1f77bcf86cd799439013",
    "company_name": "Acme Corp",
    "stage": "Series A",
    "sector": ["Technology", "SaaS"],
    "notes": "Promising startup"
  }'
```

### List Deals with Filters

```bash
curl -X GET "https://api.tractionx.com/api/v1/deals?status=new&stage=Seed&limit=50" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Update Deal Status

```bash
curl -X PUT "https://api.tractionx.com/api/v1/deals/507f1f77bcf86cd799439014" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "reviewed",
    "status_notes": "Completed initial review"
  }'
```

### Get Dashboard Summary

```bash
curl -X GET "https://api.tractionx.com/api/v1/deals/summary" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## Performance Considerations

- Database indexes are recommended on:
  - `org_id` (for organization filtering)
  - `status` (for status filtering)
  - `form_id` (for form-based queries)
  - `created_at` (for sorting)
  - `company_name` (for text search)

- Pagination is enforced with a maximum limit of 1000 records per request
- Complex searches may require additional indexes based on usage patterns

## Security Features

- Organization-level data isolation
- RBAC permission checks on all operations
- Input validation and sanitization
- ObjectId format validation
- Audit logging for all operations

## Integration Notes

- The Deal API integrates with Form and Submission services for data extraction
- Thesis service integration for automatic scoring
- Exclusion filter service integration for automated filtering
- Timeline events support user attribution for audit trails 