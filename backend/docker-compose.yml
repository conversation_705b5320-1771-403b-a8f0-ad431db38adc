version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DB_NAME=x_app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://default:<EMAIL>:14410
      - REDIS_KEY_PREFIX=tx
      - SECRET_KEY=your-secret-key-here
      - PYTHONPATH=/app
      - TEST_MODE=false
      - PYTHONUNBUFFERED=1
    depends_on:
      mongodb:
        condition: service_started
      # redis:
      #   condition: service_started
    volumes:
      - ./app:/app/app
    restart: unless-stopped
    command: poetry run uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    healthcheck:
      test: ["CMD", "poetry", "run", "python", "-c", "import requests; requests.get('http://localhost:8000/health')"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s

  worker:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DB_NAME=x_app
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_URL=redis://default:<EMAIL>:14410
      - REDIS_KEY_PREFIX=tx
      - SECRET_KEY=your-secret-key-here
      - WORKER_CONCURRENCY=2
      - PYTHONPATH=/app
      - TEST_MODE=true
      - PYTHONUNBUFFERED=1
      - DEBUG=1
      - WATCHDOG_ENABLED=1  # Enable watchdog in development
    depends_on:
      mongodb:
        condition: service_started
    volumes:
      - ./app:/app/app:delegated  # Use delegated for better performance
    restart: unless-stopped
    command: poetry run python -m app.worker  # Watchdog is enabled via WATCHDOG_ENABLED=1

  mongodb:
    image: mongo:latest
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 10s



volumes:
  mongodb_data:
  # redis_data:
